<?php
$visitas = 'includes/visitas.txt';
$ips = 'includes/ips.txt';
$ip_visitante = $_SERVER['REMOTE_ADDR'];
if (file_exists($ips)) {
    $ips_registrados = file($ips, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    if (!in_array($ip_visitante, $ips_registrados)) {
        file_put_contents($ips, $ip_visitante . PHP_EOL, FILE_APPEND); // Fixed comma issue
        if (file_exists($visitas)) {
            $contador = (int)file_get_contents($visitas);
            $contador++;
        }else{
            $contador = 1;
        }
        file_put_contents($visitas, $contador);
    }else{
        $contador = (int)file_get_contents($visitas);
    }
}else{
    file_put_contents($ips, $ip_visitante . PHP_EOL);
    file_put_contents($visitas, '1');
    $contador = 1;
}
?>