<?php
header('Content-Type: application/json');

// Ativar exibição de erros para debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Função para registrar erros em um arquivo de log
function logError($message) {
    $log_file = "../logs/db_errors.log";
    $dir = dirname($log_file);
    
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
    
    file_put_contents($log_file, date('[Y-m-d H:i:s] ') . $message . PHP_EOL, FILE_APPEND);
}

// Configurações do banco de dados
$servername = "sql112.byetcluster.com";
$username = "if0_38846001";
$password = "Dark3274";
$dbname = "if0_38846001_telas";

// Caminho para os arquivos
$includes_path = "../includes/";
$visitas_file = $includes_path . "visitas.txt";
$url_file = $includes_path . "url.txt";

// Obter dados dos arquivos
$visitas = file_exists($visitas_file) ? file_get_contents($visitas_file) : '0';
$url = file_exists($url_file) ? file_get_contents($url_file) : '';

// Obter URL completa do site atual
$protocolo = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http";
$url_completa = $protocolo . "://" . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
$url_base = $protocolo . "://" . $_SERVER['HTTP_HOST'];

// Variável para armazenar a resposta
$response = [
    'status' => 'pending',
    'visitas' => $visitas,
    'url_arquivo' => $url,
    'url_completa' => $url_completa,
    'url_base' => $url_base,
    'debug_info' => []
];

try {
    // Tentar conexão com o banco
    $response['debug_info'][] = "Tentando conectar ao banco de dados...";
    $conn = new PDO("mysql:host=$servername;dbname=$dbname", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $response['debug_info'][] = "Conexão estabelecida com sucesso";
    
    // Verificar se a tabela existe
    $tableExists = false;
    try {
        $stmt = $conn->query("SHOW TABLES LIKE 'estatisticas'");
        $tableExists = ($stmt->rowCount() > 0);
        $response['debug_info'][] = $tableExists ? "Tabela estatisticas encontrada" : "Tabela estatisticas não encontrada";
    } catch (PDOException $e) {
        $response['debug_info'][] = "Erro ao verificar tabela: " . $e->getMessage();
    }
    
    // Criar tabela se não existir
    if (!$tableExists) {
        $response['debug_info'][] = "Criando tabela estatisticas...";
        $sql = "CREATE TABLE estatisticas (
            id INT PRIMARY KEY,
            visitas INT,
            url_arquivo TEXT,
            url_completa TEXT,
            url_base TEXT,
            ultima_atualizacao DATETIME
        )";
        $conn->exec($sql);
        $response['debug_info'][] = "Tabela estatisticas criada com sucesso";
    }
    
    // Verificar se já existe um registro para atualizar
    $stmt = $conn->prepare("SELECT id FROM estatisticas WHERE id = 1");
    $stmt->execute();
    $recordExists = ($stmt->rowCount() > 0);
    $response['debug_info'][] = $recordExists ? "Registro existente encontrado, atualizando..." : "Nenhum registro encontrado, inserindo novo...";
    
    if ($recordExists) {
        // Atualizar registro existente
        $sql = "UPDATE estatisticas SET visitas = :visitas, url_arquivo = :url_arquivo, url_completa = :url_completa, url_base = :url_base, ultima_atualizacao = NOW() WHERE id = 1";
    } else {
        // Inserir novo registro
        $sql = "INSERT INTO estatisticas (id, visitas, url_arquivo, url_completa, url_base, ultima_atualizacao) VALUES (1, :visitas, :url_arquivo, :url_completa, :url_base, NOW())";
    }
    
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':visitas', $visitas);
    $stmt->bindParam(':url_arquivo', $url);
    $stmt->bindParam(':url_completa', $url_completa);
    $stmt->bindParam(':url_base', $url_base);
    
    $result = $stmt->execute();
    $response['debug_info'][] = $result ? "Operação SQL executada com sucesso" : "Falha na execução SQL";
    
    // Verificar se houve alteração efetiva no banco
    $rowCount = $stmt->rowCount();
    $response['debug_info'][] = "Número de linhas afetadas: " . $rowCount;
    
    $response['status'] = $result ? 'success' : 'error';
    $response['message'] = $result ? 'Dados salvos no banco de dados' : 'Falha ao salvar dados';
    
} catch(PDOException $e) {
    $errorMessage = "Erro PDO: " . $e->getMessage();
    logError($errorMessage);
    $response['status'] = 'error';
    $response['message'] = $errorMessage;
    $response['debug_info'][] = $errorMessage;
} catch(Exception $e) {
    $errorMessage = "Erro geral: " . $e->getMessage();
    logError($errorMessage);
    $response['status'] = 'error';
    $response['message'] = $errorMessage;
    $response['debug_info'][] = $errorMessage;
}

echo json_encode($response);
?>
