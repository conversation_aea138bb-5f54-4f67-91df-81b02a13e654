# JAD PIX Auto - Sistema de Pagamento PIX Automatizado

Sistema completo de pagamento PIX automatizado para JadLog com interface responsiva e integração com a API Prosperidade Pay.

## 🚀 Características

- **Interface Responsiva**: Design moderno com Tailwind CSS
- **Pagamento PIX**: Integração completa com API Prosperidade Pay
- **Consulta de Dados**: Sistema de consulta por CPF
- **Rate Limiting**: Proteção contra abuso de API
- **Painel Admin**: Estatísticas e monitoramento
- **Geração de NF**: Sistema de nota fiscal automatizado

## 📁 Estrutura do Projeto

```
jad-pix-auto/
├── admin/                  # Painel administrativo
├── assets/                 # Recursos estáticos
│   ├── imagens/           # Imagens e logos
│   └── js/                # Scripts JavaScript
├── consulta/              # Sistema de consulta e pagamento
├── includes/              # Arquivos de configuração
└── styles/                # Estilos CSS
```

## 🛠️ Instalação

1. Clone o repositório:
```bash
git clone https://github.com/MatxCoder/jad-pix-auto.git
cd jad-pix-auto
```

2. Configure um servidor web (Apache/Nginx) apontando para o diretório do projeto

3. Configure as credenciais da API no arquivo `includes/tk.php`

## 🔧 Configuração

### API Prosperidade Pay
- Configure a chave da API no arquivo de configuração
- Valor mínimo de transação: R$ 5,00 (500 centavos)

### Rate Limiting
- Sistema automático de proteção contra abuso
- Logs salvos em `consulta/rate_limiter/`

## 📱 Funcionalidades

### Sistema de Pagamento
- Geração automática de QR Code PIX
- Verificação de status em tempo real
- Redirecionamento automático após pagamento

### Consulta de Dados
- Busca por CPF na base de dados
- Validação de informações
- Preenchimento automático de formulários

### Painel Administrativo
- Estatísticas de uso
- Monitoramento de transações
- Logs de segurança

## 🔒 Segurança

- Rate limiting por IP
- Validação de dados de entrada
- Logs de tentativas de abuso
- Proteção contra ataques automatizados

## 📊 Monitoramento

Acesse `/admin/` para visualizar:
- Número de consultas realizadas
- Estatísticas de uso
- Logs de atividade

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 📞 Suporte

Para suporte técnico ou dúvidas sobre o projeto, abra uma issue no GitHub.

---

**Desenvolvido para JadLog** - Sistema de pagamento PIX automatizado
