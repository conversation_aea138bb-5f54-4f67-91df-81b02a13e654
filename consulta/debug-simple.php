<?php
echo "=== DEBUG SIMPLES ===\n";

// Dados da requisição real
$data = [
    "name" => "Math<PERSON>",
    "email" => "<EMAIL>", 
    "cpf" => "47020256864",
    "phone" => "1159345269"
];

echo "1. Dados:\n";
print_r($data);

// Testar validação
function validaCampos($arr) {
    if (!isset($arr['name'], $arr['cpf'], $arr['phone'])) return false;
    if (strlen(preg_replace('/\D/','',$arr['cpf'])) != 11) return false;
    if (strlen(preg_replace('/\D/','',$arr['phone'])) < 10) return false;
    if (strlen($arr['name']) < 3) return false;
    return true;
}

echo "\n2. Validação:\n";
echo "   Nome existe: " . (isset($data['name']) ? "SIM" : "NÃO") . "\n";
echo "   CPF existe: " . (isset($data['cpf']) ? "SIM" : "NÃO") . "\n";
echo "   Phone existe: " . (isset($data['phone']) ? "SIM" : "NÃO") . "\n";

$cpf_limpo = preg_replace('/\D/','',$data['cpf']);
echo "   CPF limpo: '$cpf_limpo' (tamanho: " . strlen($cpf_limpo) . ")\n";

$phone_limpo = preg_replace('/\D/','',$data['phone']);
echo "   Phone limpo: '$phone_limpo' (tamanho: " . strlen($phone_limpo) . ")\n";

echo "   Nome tamanho: " . strlen($data['name']) . "\n";

$valido = validaCampos($data);
echo "   Validação: " . ($valido ? "PASSOU" : "FALHOU") . "\n";

if (!$valido) {
    echo "\n3. Problemas encontrados:\n";
    if (!isset($data['name'], $data['cpf'], $data['phone'])) {
        echo "   - Campos obrigatórios faltando\n";
    }
    if (strlen(preg_replace('/\D/','',$data['cpf'])) != 11) {
        echo "   - CPF deve ter 11 dígitos\n";
    }
    if (strlen(preg_replace('/\D/','',$data['phone'])) < 10) {
        echo "   - Telefone deve ter pelo menos 10 dígitos\n";
    }
    if (strlen($data['name']) < 3) {
        echo "   - Nome deve ter pelo menos 3 caracteres\n";
    }
}

echo "\n=== FIM ===\n";
?>
