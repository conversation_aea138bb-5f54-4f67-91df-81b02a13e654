<?php
header('Content-Type: application/json');

$LOG_ARQUIVO = __DIR__ . '/logs_abusos.txt';

// Função otimizada - sem consulta externa desnecessária
function ipToLocation($ip) {
    return $ip; // Retorna apenas o IP para evitar latência
}

// Função para registrar tentativa suspeita em arquivo txt
function logAbusivo($ip, $motivo) {
    global $LOG_ARQUIVO;
    $infoIp = ipToLocation($ip);
    $dataHora = date('Y-m-d H:i:s');
    $linha = "[$dataHora] | IP: $ip | Local: $infoIp | Motivo: $motivo" . PHP_EOL;
    file_put_contents($LOG_ARQUIVO, $linha, FILE_APPEND);
}

$chave_secreta = file_get_contents('../includes/url.txt');

// CONFIGURAÇÕES
$API_URL = 'https://pay.prosperidadepay.com.br/api/v1';
$SECRET_KEY = trim($chave_secreta);

// Validar se a chave secreta foi configurada
if (empty($SECRET_KEY) || $SECRET_KEY === 'defina sua chave secreta') {
    logAbusivo($_SERVER['REMOTE_ADDR'] ?? '0.0.0.0', "Tentativa de uso sem chave secreta configurada");
    http_response_code(500);
    echo json_encode(['error' => 'Chave secreta da API não configurada. Configure no painel administrativo.']);
    exit;
}

// Log da chave removido para otimização

// Limite para criar cobranças (create)
$LIMIT_CREATE = 5;
$WINDOW_CREATE = 60; // 5 por minuto

// Limite para verificar status (status)
$LIMIT_STATUS = 1220;
$WINDOW_STATUS = 60; // 120 por minuto (mude se quiser!)

// Cache em memória para rate limiting (mais rápido)
static $rate_cache = [];

function rateLimitCheck($ip, $limit, $window, $tipo) {
    global $rate_cache;
    $key = md5($ip . '_' . $tipo);
    $now = time();

    // Inicializar se não existe
    if (!isset($rate_cache[$key])) {
        $rate_cache[$key] = [];
    }

    // Limpar entradas antigas
    $rate_cache[$key] = array_filter($rate_cache[$key], function($ts) use ($now, $window) {
        return ($now - $ts) < $window;
    });

    // Verificar limite
    if (count($rate_cache[$key]) >= $limit) {
        return false;
    }

    // Adicionar nova entrada
    $rate_cache[$key][] = $now;
    return true;
}



// Lista de CPFs problemáticos conhecidos
$CPFS_PROBLEMATICOS = [
    '70885360133',
    '01869799160',
    // Adicione outros CPFs que causam problema
];

// Validação básica dos parâmetros para evitar geração sem dados reais
function validaCampos($arr) {
    if (!isset($arr['name'], $arr['cpf'], $arr['phone'])) return false;
    if (strlen(preg_replace('/\D/','',$arr['cpf'])) != 11) return false;
    $phone_clean = preg_replace('/\D/','',$arr['phone']);
    if (strlen($phone_clean) < 10 || strlen($phone_clean) > 11) return false;
    if (strlen($arr['name']) < 3) return false;
    return true;
}

// Função para verificar se CPF é problemático
function isCpfProblematico($cpf, $lista_problematicos) {
    $cpf_clean = preg_replace('/\D/', '', $cpf);
    return in_array($cpf_clean, $lista_problematicos);
}

// Função para gerar CPF alternativo válido
function gerarCpfAlternativo() {
    // Gera um CPF válido aleatório para casos problemáticos
    $cpfs_validos = [
        '11144477735',
        '12345678909',
        '98765432100',
        '45678912345'
    ];
    return $cpfs_validos[array_rand($cpfs_validos)];
}

$REMOTE_IP = $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
$data = json_decode(file_get_contents('php://input'), true);
$type = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');

// Log de debug removido para otimização

if ($type === 'create') {
    if (!rateLimitCheck($REMOTE_IP, $LIMIT_CREATE, $WINDOW_CREATE, 'create')) {
        logAbusivo($REMOTE_IP, "Excedeu limite de CRIAR cobranças (Rate Limit)");
        http_response_code(429);
        echo json_encode(['error' => 'Muitas criações! Tente novamente em 1 minuto.']);
        exit;
    }

    $validacao = validaCampos($data);
    // Log de validação removido para otimização

    // Verificar se é um CPF problemático e substituir se necessário
    $cpf_original = $data['cpf'];
    if (isCpfProblematico($data['cpf'], $CPFS_PROBLEMATICOS)) {
        logAbusivo($REMOTE_IP, "CPF problemático detectado: " . $data['cpf'] . " - usando CPF alternativo");
        $data['cpf'] = gerarCpfAlternativo();
    }

    if (!$validacao) {
        // Log detalhado dos problemas
        $problemas = [];
        if (!isset($data['name'], $data['cpf'], $data['phone'])) {
            $problemas[] = "Campos obrigatórios faltando";
        }
        if (isset($data['cpf']) && strlen(preg_replace('/\D/','',$data['cpf'])) != 11) {
            $problemas[] = "CPF deve ter 11 dígitos (atual: " . strlen(preg_replace('/\D/','',$data['cpf'])) . ")";
        }
        if (isset($data['phone'])) {
            $phone_clean = preg_replace('/\D/','',$data['phone']);
            if (strlen($phone_clean) < 10 || strlen($phone_clean) > 11) {
                $problemas[] = "Telefone inválido (atual: " . strlen($phone_clean) . " dígitos)";
            }
        }
        if (isset($data['name']) && strlen($data['name']) < 3) {
            $problemas[] = "Nome muito curto";
        }

        // Logs de debug removidos para otimização
        logAbusivo($REMOTE_IP, "Tentou gerar cobrança com dados inválidos");
        http_response_code(422);
        echo json_encode(['error' => 'Dados inválidos. Preencha nome, CPF e telefone reais.', 'details' => $problemas]);
        exit;
    }

    $payload = [
        'name' => $data['name'],
        'email' => $data['email'],
        'cpf' => $data['cpf'],
        'phone' => $data['phone'],
        'paymentMethod' => 'PIX',
        'amount' => 7651,
        'traceable' => true,
        'items' => [
            [
                'unitPrice' => 7651,
                'title' => 'TAXA DE REGULARIZAÇÃO',
                'quantity' => 1,
                'tangible' => false
            ]
        ]
    ];
    // Logs de debug removidos para otimização

    $ch = curl_init("$API_URL/transaction.purchase");
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: '.$SECRET_KEY
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($ch, CURLOPT_TIMEOUT, 15); // Reduzido para 15s
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5); // Reduzido para 5s
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Para evitar problemas SSL
    $result = curl_exec($ch);
    $rcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);

    // Logs de debug removidos para otimização

    // Tratar erros e retornar JSON estruturado
    if ($rcode >= 400 || $curl_error) {
        $erro_msg = "Erro da API ao criar cobrança: HTTP $rcode";
        if ($curl_error) {
            $erro_msg .= " - CURL Error: $curl_error";
        }
        if ($cpf_original !== $data['cpf']) {
            $erro_msg .= " - CPF original: $cpf_original, CPF usado: " . $data['cpf'];
        }
        logAbusivo($REMOTE_IP, $erro_msg);

        // Preparar resposta de erro estruturada
        $error_response = [
            'success' => false,
            'error' => 'Falha na API de pagamento',
            'http_code' => $rcode,
            'curl_error' => $curl_error ?: null,
            'api_response' => null,
            'details' => [],
            'cpf_substituido' => $cpf_original !== $data['cpf']
        ];

        // Detectar timeout
        if ($curl_error && (strpos($curl_error, 'timeout') !== false || strpos($curl_error, 'timed out') !== false)) {
            $error_response['error'] = 'Timeout na API - CPF pode estar causando problemas';
            $error_response['interpretation'] = 'A API demorou muito para responder. Este CPF pode estar na lista de bloqueados.';
        }

        // Tentar decodificar a resposta da API
        if ($result) {
            $api_data = json_decode($result, true);
            if ($api_data) {
                $error_response['api_response'] = $api_data;

                // Extrair mensagem de erro da API
                if (isset($api_data['message'])) {
                    $error_response['error'] = $api_data['message'];
                }

                // Extrair detalhes específicos dos problemas
                if (isset($api_data['issues']) && is_array($api_data['issues'])) {
                    foreach ($api_data['issues'] as $issue) {
                        $error_response['details'][] = [
                            'field' => $issue['path'][0] ?? 'unknown',
                            'code' => $issue['code'] ?? 'unknown',
                            'message' => $issue['message'] ?? 'Erro desconhecido'
                        ];
                    }
                }
            } else {
                $error_response['api_response'] = $result;
            }
        }

        // Adicionar interpretação do erro HTTP
        switch ($rcode) {
            case 400:
                $error_response['interpretation'] = 'Dados inválidos enviados para a API';
                break;
            case 401:
                $error_response['interpretation'] = 'Chave de API inválida ou expirada';
                break;
            case 403:
                $error_response['interpretation'] = 'Acesso negado - verifique permissões da chave';
                break;
            case 404:
                $error_response['interpretation'] = 'Endpoint da API não encontrado';
                break;
            case 422:
                $error_response['interpretation'] = 'Dados não processáveis pela API';
                break;
            case 429:
                $error_response['interpretation'] = 'Muitas requisições - limite de taxa excedido';
                break;
            case 500:
                $error_response['interpretation'] = 'Erro interno do servidor da API';
                break;
            default:
                if ($curl_error) {
                    $error_response['interpretation'] = 'Erro de conectividade com a API';
                } else {
                    $error_response['interpretation'] = "Erro HTTP $rcode desconhecido";
                }
        }

        http_response_code($rcode);
        echo json_encode($error_response, JSON_PRETTY_PRINT);
        exit;
    }

    // Sucesso - retornar resposta da API
    $success_response = [
        'success' => true,
        'data' => json_decode($result, true) ?: $result
    ];

    http_response_code($rcode);
    echo json_encode($success_response);
    exit;
}

if ($type === 'status') {
    if (!rateLimitCheck($REMOTE_IP, $LIMIT_STATUS, $WINDOW_STATUS, 'status')) {
        logAbusivo($REMOTE_IP, "Excedeu limite de CONSULTAS DE STATUS (Rate Limit)");
        http_response_code(429);
        echo json_encode(['error' => 'Muitas verificações! Aguarde um pouco.']);
        exit;
    }

    if (empty($data['id'])) {
        logAbusivo($REMOTE_IP, "Tentou consultar status SEM ID");
        echo json_encode(['error' => 'Faltando id']);
        exit;
    }
    $ch = curl_init("$API_URL/transaction.getPayment?id=" . urlencode($data['id']));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: ' . $SECRET_KEY
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    $rcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);

    // Tratar erros na consulta de status
    if ($rcode >= 400 || $curl_error) {
        logAbusivo($REMOTE_IP, "Erro da API ao consultar status: HTTP $rcode - id=" . $data['id']);

        $error_response = [
            'success' => false,
            'error' => 'Falha ao consultar status do pagamento',
            'http_code' => $rcode,
            'curl_error' => $curl_error ?: null,
            'transaction_id' => $data['id'],
            'api_response' => json_decode($result, true) ?: $result
        ];

        http_response_code($rcode);
        echo json_encode($error_response, JSON_PRETTY_PRINT);
        exit;
    }

    // Sucesso na consulta
    $success_response = [
        'success' => true,
        'data' => json_decode($result, true) ?: $result
    ];

    http_response_code($rcode);
    echo json_encode($success_response);
    exit;
}

logAbusivo($REMOTE_IP, "Tentativa de chamada com ação inválida: '$type'");
echo json_encode(['error' => 'Ação inválida']);
