<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Função para log
function logRequest($message) {
    $log = "[" . date('Y-m-d H:i:s') . "] $message" . PHP_EOL;
    file_put_contents(__DIR__ . '/logs_dados.txt', $log, FILE_APPEND);
}

// Verifica se o CPF foi enviado
if (!isset($_GET['cpf'])) {
    logRequest("CPF não informado");
    http_response_code(400);
    echo json_encode(['error' => 'CPF não informado']);
    exit;
}

$cpf = $_GET['cpf'];
// Remove caracteres não numéricos do CPF
$cpf = preg_replace('/\D/', '', $cpf);

// Verifica se o CPF tem 11 dígitos
if (strlen($cpf) !== 11) {
    logRequest("CPF inválido: $cpf");
    http_response_code(400);
    echo json_encode(['error' => 'CPF deve ter 11 dígitos']);
    exit;
}

logRequest("Consultando CPF: $cpf");

// Endpoint da API MyTrust
$api_url = "https://api.mytrust.space/v1/cpf/{$cpf}";

// Faz a requisição à API usando cURL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'x-trust-key: sk_01jv74jy9vqw2b183cvmrmptwg01jv74jy9wmgh950m9mstkbf61',
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

logRequest("API MyTrust - HTTP Code: $http_code");

// Verifica se houve erro na requisição cURL
if ($curl_error) {
    logRequest("CURL Error: $curl_error");
    http_response_code(500);
    echo json_encode(['error' => 'Erro de conectividade com a API']);
    exit;
}

// Verifica se a requisição foi bem-sucedida
if ($http_code == 200) {
    // Decodifica o JSON retornado pela API
    $data = json_decode($response, true);
    
    logRequest("Resposta da API: " . substr($response, 0, 200));
    
    // Verifica se a resposta contém os dados esperados
    if (isset($data['data'])) {
        $dados = $data['data'];
        
        // Formatar os dados para o formato esperado
        $dadosFormatados = [
            'success' => true,
            'data' => [
                'DADOS_PESSOAIS' => [
                    'NOME' => $dados['DADOS_PESSOAIS']['NOME'] ?? '',
                    'CPF' => $dados['DADOS_PESSOAIS']['CPF'] ?? $cpf,
                    'DATA_NASCIMENTO' => $dados['DADOS_PESSOAIS']['DATA_NASCIMENTO'] ?? ''
                ],
                'TELEFONES' => $dados['TELEFONES'] ?? [
                    [
                        'TELEFONE' => ''
                    ]
                ]
            ]
        ];
        
        logRequest("Dados encontrados para CPF: $cpf");
        echo json_encode($dadosFormatados);
        exit;
    } else {
        logRequest("Dados não encontrados na resposta da API");
        http_response_code(404);
        echo json_encode(['error' => 'Dados não encontrados']);
        exit;
    }
} else {
    logRequest("API retornou erro HTTP: $http_code");
    http_response_code($http_code);
    echo json_encode(['error' => 'Serviço temporariamente indisponível', 'http_code' => $http_code]);
    exit;
}
?>
