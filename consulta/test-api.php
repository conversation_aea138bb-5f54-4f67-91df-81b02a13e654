<?php
header('Content-Type: application/json');

echo "=== TESTE DE DIAGNÓSTICO DA API ===\n\n";

// 1. Verificar se o arquivo de chave existe
$chave_file = '../includes/url.txt';
echo "1. Verificando arquivo de chave:\n";
echo "   Arquivo: $chave_file\n";
echo "   Existe: " . (file_exists($chave_file) ? "SIM" : "NÃO") . "\n";

if (file_exists($chave_file)) {
    $chave_secreta = file_get_contents($chave_file);
    echo "   Conteúdo: '" . trim($chave_secreta) . "'\n";
    echo "   Tamanho: " . strlen(trim($chave_secreta)) . " caracteres\n";
} else {
    echo "   ERRO: Arquivo não encontrado!\n";
    exit;
}

echo "\n";

// 2. Configurar API
$API_URL = 'https://pay.prosperidadepay.com.br/api/v1';
$SECRET_KEY = trim($chave_secreta);

echo "2. Configuração da API:\n";
echo "   URL: $API_URL\n";
echo "   Chave (primeiros 20 chars): " . substr($SECRET_KEY, 0, 20) . "...\n";
echo "   Chave válida: " . ($SECRET_KEY !== 'defina sua chave secreta' ? "SIM" : "NÃO") . "\n";

if ($SECRET_KEY === 'defina sua chave secreta') {
    echo "   ERRO: Chave não configurada!\n";
    echo "\n=== SOLUÇÃO ===\n";
    echo "1. Acesse o painel admin: /admin/index.php\n";
    echo "2. Faça login com: admin / jadlog2023\n";
    echo "3. Configure a chave da Prosperidade Pay no campo 'GATE'\n";
    exit;
}

echo "\n";

// 3. Teste de conectividade
echo "3. Teste de conectividade:\n";
$test_url = $API_URL . '/test';
echo "   Testando: $test_url\n";

$ch = curl_init($test_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: ' . $SECRET_KEY
]);

$result = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

echo "   HTTP Code: $http_code\n";
echo "   CURL Error: " . ($curl_error ?: "Nenhum") . "\n";
echo "   Response: " . substr($result, 0, 200) . "\n";

echo "\n";

// 4. Teste de payload
echo "4. Teste de payload de exemplo:\n";
$payload = [
    'name' => 'Teste Usuario',
    'email' => '<EMAIL>',
    'cpf' => '12345678901',
    'phone' => '11999999999',
    'paymentMethod' => 'PIX',
    'amount' => 500, // R$ 5,00 (valor mínimo)
    'traceable' => true,
    'items' => [
        [
            'unitPrice' => 500,
            'title' => 'TESTE DE CONEXÃO',
            'quantity' => 1,
            'tangible' => false
        ]
    ]
];

echo "   Payload: " . json_encode($payload, JSON_PRETTY_PRINT) . "\n";

echo "\n";

// 5. Teste real da API
echo "5. Teste real da API (transaction.purchase):\n";
$endpoint = $API_URL . '/transaction.purchase';
echo "   Endpoint: $endpoint\n";

$ch = curl_init($endpoint);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: ' . $SECRET_KEY
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$result = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

echo "   HTTP Code: $http_code\n";
echo "   CURL Error: " . ($curl_error ?: "Nenhum") . "\n";
echo "   Response: " . $result . "\n";

echo "\n=== FIM DO TESTE ===\n";

// Interpretação dos resultados
echo "\n=== INTERPRETAÇÃO ===\n";
if ($http_code == 200 || $http_code == 201) {
    echo "✅ SUCESSO: API funcionando corretamente!\n";
} elseif ($http_code == 401) {
    echo "❌ ERRO 401: Chave de API inválida ou expirada\n";
} elseif ($http_code == 403) {
    echo "❌ ERRO 403: Acesso negado - verifique permissões da chave\n";
} elseif ($http_code == 404) {
    echo "❌ ERRO 404: Endpoint não encontrado - verifique a URL\n";
} elseif ($http_code == 400) {
    echo "❌ ERRO 400: Dados inválidos no payload\n";
    echo "   Verifique: valor mínimo R$ 5,00, CPF válido, campos obrigatórios\n";
} elseif ($http_code == 422) {
    echo "❌ ERRO 422: Dados inválidos no payload\n";
} elseif ($http_code == 0) {
    echo "❌ ERRO DE CONEXÃO: Não foi possível conectar à API\n";
} else {
    echo "❌ ERRO $http_code: Erro desconhecido\n";
}
?>
