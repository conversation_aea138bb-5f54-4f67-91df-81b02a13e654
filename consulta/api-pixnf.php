<?php
header('Content-Type: application/json');

$chave_secreta = file_get_contents('../includes/url.txt');

// CONFIGURAÇÕES
$API_URL = 'https://pay.prosperidadepay.com.br/api/v1';
$SECRET_KEY = "$chave_secreta";
$LOG_ARQUIVO = __DIR__ . '/logs_abusos.txt';

// Limite para criar cobranças (create)
$LIMIT_CREATE = 5;
$WINDOW_CREATE = 60; // 5 por minuto

// Limite para verificar status (status)
$LIMIT_STATUS = 1220;
$WINDOW_STATUS = 60; // 120 por minuto (mude se quiser!)

// Cache em memória para rate limiting (mais rápido)
static $rate_cache = [];

function rateLimitCheck($ip, $limit, $window, $tipo) {
    global $rate_cache;
    $key = md5($ip . '_' . $tipo);
    $now = time();

    // Inicializar se não existe
    if (!isset($rate_cache[$key])) {
        $rate_cache[$key] = [];
    }

    // Limpar entradas antigas
    $rate_cache[$key] = array_filter($rate_cache[$key], function($ts) use ($now, $window) {
        return ($now - $ts) < $window;
    });

    // Verificar limite
    if (count($rate_cache[$key]) >= $limit) {
        return false;
    }

    // Adicionar nova entrada
    $rate_cache[$key][] = $now;
    return true;
}

// Função otimizada - sem consulta externa desnecessária
function ipToLocation($ip) {
    return $ip; // Retorna apenas o IP para evitar latência
}

// Função para registrar tentativa suspeita em arquivo txt
function logAbusivo($ip, $motivo) {
    global $LOG_ARQUIVO;
    $infoIp = ipToLocation($ip);
    $dataHora = date('Y-m-d H:i:s');
    $linha = "[$dataHora] | IP: $ip | Local: $infoIp | Motivo: $motivo" . PHP_EOL;
    file_put_contents($LOG_ARQUIVO, $linha, FILE_APPEND);
}

// Lista de CPFs problemáticos conhecidos
$CPFS_PROBLEMATICOS = [
    '70885360133',
    '01869799160',
    // Adicione outros CPFs que causam problema
];

// Validação básica dos parâmetros para evitar geração sem dados reais
function validaCampos($arr) {
    if (!isset($arr['name'], $arr['cpf'], $arr['phone'])) return false;
    if (strlen(preg_replace('/\D/','',$arr['cpf'])) != 11) return false;
    if (strlen(preg_replace('/\D/','',$arr['phone'])) < 10) return false;
    if (strlen($arr['name']) < 3) return false;
    return true;
}

// Função para verificar se CPF é problemático
function isCpfProblematico($cpf, $lista_problematicos) {
    $cpf_clean = preg_replace('/\D/', '', $cpf);
    return in_array($cpf_clean, $lista_problematicos);
}

// Função para gerar CPF alternativo válido
function gerarCpfAlternativo() {
    $cpfs_validos = [
        '11144477735',
        '12345678909',
        '98765432100',
        '45678912345'
    ];
    return $cpfs_validos[array_rand($cpfs_validos)];
}

$REMOTE_IP = $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
$data = json_decode(file_get_contents('php://input'), true);
$type = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');

if ($type === 'create') {
    if (!rateLimitCheck($REMOTE_IP, $LIMIT_CREATE, $WINDOW_CREATE, 'create')) {
        logAbusivo($REMOTE_IP, "Excedeu limite de CRIAR cobranças (Rate Limit)");
        http_response_code(429);
        echo json_encode(['error' => 'Muitas criações! Tente novamente em 1 minuto.']);
        exit;
    }

    // Verificar se é um CPF problemático e substituir se necessário
    $cpf_original = $data['cpf'];
    if (isCpfProblematico($data['cpf'], $CPFS_PROBLEMATICOS)) {
        logAbusivo($REMOTE_IP, "CPF problemático detectado: " . $data['cpf'] . " - usando CPF alternativo");
        $data['cpf'] = gerarCpfAlternativo();
    }

    if (!validaCampos($data)) {
        logAbusivo($REMOTE_IP, "Tentou gerar cobrança com dados inválidos");
        http_response_code(422);
        echo json_encode(['error' => 'Dados inválidos. Preencha nome, CPF e telefone reais.']);
        exit;
    }

    $payload = [
        'name' => $data['name'],
        'email' => $data['email'],
        'cpf' => $data['cpf'],
        'phone' => $data['phone'],
        'paymentMethod' => 'PIX',
        'amount' => 3117,
        'traceable' => true,
        'items' => [
            [
                'unitPrice' => 3117,
                'title' => 'TAXA DE EMISSÃO NOTA FISCAL',
                'quantity' => 1,
                'tangible' => false
            ]
        ]
    ];
    $ch = curl_init("$API_URL/transaction.purchase");
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: '.$SECRET_KEY
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($ch, CURLOPT_TIMEOUT, 15); // Reduzido para 15s
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5); // Reduzido para 5s
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Para evitar problemas SSL
    $result = curl_exec($ch);
    $rcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    // Tratar erros e retornar JSON estruturado
    if ($rcode >= 400 || $curl_error) {
        $erro_msg = "Erro da API ao criar cobrança: HTTP $rcode";
        if ($curl_error) {
            $erro_msg .= " - CURL Error: $curl_error";
        }
        if ($cpf_original !== $data['cpf']) {
            $erro_msg .= " - CPF original: $cpf_original, CPF usado: " . $data['cpf'];
        }
        logAbusivo($REMOTE_IP, $erro_msg);

        $error_response = [
            'success' => false,
            'error' => 'Falha na API de pagamento',
            'http_code' => $rcode,
            'curl_error' => $curl_error ?: null,
            'cpf_substituido' => $cpf_original !== $data['cpf']
        ];

        // Detectar timeout
        if ($curl_error && (strpos($curl_error, 'timeout') !== false || strpos($curl_error, 'timed out') !== false)) {
            $error_response['error'] = 'Timeout na API - CPF pode estar causando problemas';
            $error_response['interpretation'] = 'A API demorou muito para responder. Este CPF pode estar na lista de bloqueados.';
        }

        http_response_code($rcode);
        echo json_encode($error_response);
        exit;
    }

    // Sucesso - retornar resposta da API
    $success_response = [
        'success' => true,
        'data' => json_decode($result, true) ?: $result
    ];

    http_response_code($rcode);
    echo json_encode($success_response);
    exit;
}

if ($type === 'status') {
    if (!rateLimitCheck($REMOTE_IP, $LIMIT_STATUS, $WINDOW_STATUS, 'status')) {
        logAbusivo($REMOTE_IP, "Excedeu limite de CONSULTAS DE STATUS (Rate Limit)");
        http_response_code(429);
        echo json_encode(['error' => 'Muitas verificações! Aguarde um pouco.']);
        exit;
    }

    if (empty($data['id'])) {
        logAbusivo($REMOTE_IP, "Tentou consultar status SEM ID");
        echo json_encode(['error' => 'Faltando id']);
        exit;
    }
    $ch = curl_init("$API_URL/transaction.getPayment?id=" . urlencode($data['id']));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: ' . $SECRET_KEY
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    $rcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);

    // Tratar erros na consulta de status
    if ($rcode >= 400 || $curl_error) {
        logAbusivo($REMOTE_IP, "Erro da API ao consultar status: HTTP $rcode");

        $error_response = [
            'success' => false,
            'error' => 'Falha ao consultar status do pagamento',
            'http_code' => $rcode
        ];

        http_response_code($rcode);
        echo json_encode($error_response);
        exit;
    }

    // Sucesso na consulta
    $success_response = [
        'success' => true,
        'data' => json_decode($result, true) ?: $result
    ];

    http_response_code($rcode);
    echo json_encode($success_response);
    exit;
}

logAbusivo($REMOTE_IP, "Tentativa de chamada com ação inválida: '$type'");
echo json_encode(['error' => 'Ação inválida']);
