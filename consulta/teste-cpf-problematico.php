<?php
// Teste para CPF problemático 70885360133

echo "=== TESTE CPF PROBLEMÁTICO ===\n";

// Simular requisição para api-pix.php
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';
$_GET['action'] = 'create';

$data = [
    'name' => 'Teste Usuario',
    'email' => '<EMAIL>',
    'cpf' => '70885360133', // CPF problemático
    'phone' => '11999999999'
];

echo "CPF original: " . $data['cpf'] . "\n";

// Simular input
file_put_contents('php://input', json_encode($data));

echo "Testando api-pix.php...\n";

ob_start();
$start = microtime(true);

try {
    include 'api-pix.php';
} catch (Exception $e) {
    echo "Exceção capturada: " . $e->getMessage() . "\n";
}

$end = microtime(true);
$output = ob_get_clean();

echo "Tempo de execução: " . round(($end - $start) * 1000, 2) . "ms\n";
echo "Resposta:\n";
echo $output . "\n";

// Verificar se houve substituição de CPF
if (strpos($output, 'cpf_substituido') !== false) {
    echo "✅ CPF foi substituído automaticamente!\n";
} else {
    echo "❌ CPF não foi substituído\n";
}

// Verificar se houve timeout
if (strpos($output, 'timeout') !== false || strpos($output, 'Timeout') !== false) {
    echo "⚠️ Timeout detectado\n";
} else {
    echo "✅ Sem timeout\n";
}

echo "\n=== FIM DO TESTE ===\n";
?>
