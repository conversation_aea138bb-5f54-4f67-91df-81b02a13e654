<?php
// Teste da requisição real
header('Content-Type: text/plain');

echo "=== TESTE DA REQUISIÇÃO REAL ===\n\n";

// Simular os dados da requisição real
$data = [
    "name" => "<PERSON><PERSON>",
    "email" => "<EMAIL>", 
    "cpf" => "47020256864",
    "cpfFormatted" => "470.202.568-64",
    "phone" => "1159345269",
    "phoneFormatted" => "(11) 59345269"
];

echo "1. Dados recebidos:\n";
echo json_encode($data, JSON_PRETTY_PRINT) . "\n\n";

// Verificar chave
$chave_secreta = file_get_contents('../includes/url.txt');
$SECRET_KEY = trim($chave_secreta);

echo "2. Chave da API:\n";
echo "   Chave: " . substr($SECRET_KEY, 0, 20) . "...\n";
echo "   Tamanho: " . strlen($SECRET_KEY) . " caracteres\n\n";

// Montar payload como no código real
$payload = [
    'name' => $data['name'],
    'email' => $data['email'],
    'cpf' => $data['cpf'],
    'phone' => $data['phone'],
    'paymentMethod' => 'PIX',
    'amount' => 7651,
    'traceable' => true,
    'items' => [
        [
            'unitPrice' => 7651,
            'title' => 'TAXA DE REGULARIZAÇÃO',
            'quantity' => 1,
            'tangible' => false
        ]
    ]
];

echo "3. Payload montado:\n";
echo json_encode($payload, JSON_PRETTY_PRINT) . "\n\n";

// Fazer a requisição
$API_URL = 'https://pay.prosperidadepay.com.br/api/v1';
$endpoint = $API_URL . '/transaction.purchase';

echo "4. Fazendo requisição para: $endpoint\n";

$ch = curl_init($endpoint);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: ' . $SECRET_KEY
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

$result = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

echo "5. Resultado:\n";
echo "   HTTP Code: $http_code\n";
echo "   CURL Error: " . ($curl_error ?: "Nenhum") . "\n";
echo "   Response: $result\n\n";

// Analisar resposta
if ($http_code == 200 || $http_code == 201) {
    echo "✅ SUCESSO: Transação criada!\n";
    $response_data = json_decode($result, true);
    if ($response_data && isset($response_data['id'])) {
        echo "   ID da transação: " . $response_data['id'] . "\n";
    }
} else {
    echo "❌ ERRO: HTTP $http_code\n";
    $response_data = json_decode($result, true);
    if ($response_data && isset($response_data['message'])) {
        echo "   Mensagem: " . $response_data['message'] . "\n";
    }
    if ($response_data && isset($response_data['issues'])) {
        echo "   Problemas:\n";
        foreach ($response_data['issues'] as $issue) {
            echo "     - " . $issue['message'] . "\n";
        }
    }
}

echo "\n=== FIM DO TESTE ===\n";
?>
