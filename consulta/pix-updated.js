    // Função atualizada para compatibilidade com a nova resposta da API
    async function createPixTransaction(formData) {
        try {
            const response = await fetch(`${API_URL_LOCAL}?action=create`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formData)
            });
            
            const result = await response.json();
            
            // Verificar se a resposta indica sucesso
            if (!result.success) {
                // Construir mensagem de erro detalhada
                let errorMessage = result.error || 'Falha ao processar a transação';
                
                if (result.details && result.details.length > 0) {
                    const detailMessages = result.details.map(detail => 
                        `${detail.field}: ${detail.message}`
                    ).join(', ');
                    errorMessage += ` (${detailMessages})`;
                }
                
                if (result.interpretation) {
                    errorMessage += ` - ${result.interpretation}`;
                }
                
                throw new Error(errorMessage);
            }
            
            // Retornar os dados da transação
            return result.data;
        } catch (error) {
            console.error('Erro ao criar PIX:', error);
            throw error; // Re-throw para que o erro seja tratado no chamador
        }
    }

    async function checkPaymentStatus(paymentId) {
        try {
            const response = await fetch(`${API_URL_LOCAL}?action=status`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ id: paymentId })
            });
            
            const result = await response.json();
            
            // Verificar se a resposta indica sucesso
            if (!result.success) {
                console.warn('Erro ao verificar status:', result.error);
                return { status: 'PENDING' }; // Retorna pendente para não quebrar
            }
            
            // Retornar os dados do status
            return result.data;
        } catch (error) {
            console.error('Erro ao verificar status:', error);
            // Retorna pendente para não quebrar
            return { status: 'PENDING' };
        }
    }

    function startPaymentStatusCheck(paymentId) {
        const interval = setInterval(async function() {
            const paymentInfo = await checkPaymentStatus(paymentId);
            if (!paymentInfo) return;
            
            // Verificar status da transação
            if (paymentInfo.status === 'APPROVED') {
                paymentStatus.textContent = 'Aprovado';
                paymentStatus.classList.remove('text-yellow-500');
                paymentStatus.classList.add('text-green-500');
                const statusIndicator = document.querySelector('.animate-pulse');
                statusIndicator.classList.remove('bg-yellow-400', 'animate-pulse');
                statusIndicator.classList.add('bg-green-500');
                const statusBox = document.querySelector('.bg-blue-50');
                statusBox.classList.remove('bg-blue-50');
                statusBox.classList.add('bg-green-50');
                clearInterval(interval);
                setTimeout(function() {
                    const queryString = window.location.search;
                    window.location.href = 'nf.php' + queryString;
                }, 2000);
            } else if (paymentInfo.status === 'REJECTED') {
                paymentStatus.textContent = 'Rejeitado';
                paymentStatus.classList.remove('text-yellow-500');
                paymentStatus.classList.add('text-red-500');
                const statusIndicator = document.querySelector('.animate-pulse');
                statusIndicator.classList.remove('bg-yellow-400', 'animate-pulse');
                statusIndicator.classList.add('bg-red-500');
                const statusBox = document.querySelector('.bg-blue-50');
                statusBox.classList.remove('bg-blue-50');
                statusBox.classList.add('bg-red-50');
                clearInterval(interval);
            } else if (paymentInfo.status === 'REFUNDED') {
                paymentStatus.textContent = 'Reembolsado';
                paymentStatus.classList.remove('text-yellow-500');
                paymentStatus.classList.add('text-orange-500');
                const statusIndicator = document.querySelector('.animate-pulse');
                statusIndicator.classList.remove('bg-yellow-400', 'animate-pulse');
                statusIndicator.classList.add('bg-orange-500');
                const statusBox = document.querySelector('.bg-blue-50');
                statusBox.classList.remove('bg-blue-50');
                statusBox.classList.add('bg-orange-50');
                clearInterval(interval);
            }
            // Se ainda estiver pendente, continuar verificando
        }, 1000);
        
        // Timeout de 20 minutos (1200000ms)
        setTimeout(function() {
            clearInterval(interval);
            if (paymentStatus.textContent === 'Pendente') {
                paymentStatus.textContent = 'Expirado';
                paymentStatus.classList.remove('text-yellow-500');
                paymentStatus.classList.add('text-gray-500');
                const statusIndicator = document.querySelector('.animate-pulse');
                statusIndicator.classList.remove('bg-yellow-400', 'animate-pulse');
                statusIndicator.classList.add('bg-gray-500');
                const statusBox = document.querySelector('.bg-blue-50');
                statusBox.classList.remove('bg-blue-50');
                statusBox.classList.add('bg-gray-50');
            }
        }, 1200000);
    }
