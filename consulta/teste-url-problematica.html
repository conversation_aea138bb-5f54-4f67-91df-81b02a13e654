<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste URL Problemática</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .problematic { border-color: #dc3545; background: #f8d7da; }
        .working { border-color: #28a745; background: #d4edda; }
        .result { margin-top: 10px; padding: 10px; background: #f9f9f9; border-radius: 3px; }
        button { background: #007cba; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Teste de URLs Problemáticas</h1>
        
        <div class="test-case problematic">
            <h3>❌ URL Problemática (CPF 70885360133 - SEM TELEFONE)</h3>
            <button onclick="testarUrlProblematica()">Testar URL Problemática</button>
            <div id="resultado-problematica" class="result" style="display: none;">
                <h4>Resultado:</h4>
                <pre id="dados-problematica"></pre>
            </div>
        </div>
        
        <div class="test-case working">
            <h3>✅ URL Funcionando (CPF 47020256864 - COM TELEFONES)</h3>
            <button onclick="testarUrlFuncionando()">Testar URL Funcionando</button>
            <div id="resultado-funcionando" class="result" style="display: none;">
                <h4>Resultado:</h4>
                <pre id="dados-funcionando"></pre>
            </div>
        </div>
    </div>

    <script>
        // Simular a função getUrlParams do pix.php
        function getUrlParams(queryString) {
            const params = {};
            const urlParams = new URLSearchParams(queryString);
            params.name = urlParams.get('DADOS_PESSOAIS[NOME]') || '';
            params.email = '<EMAIL>';
            let cpf = urlParams.get('DADOS_PESSOAIS[CPF]') || '';
            params.cpf = cpf.replace(/[^\d]/g, '') || "";
            params.cpfFormatted = cpf || "";
            
            // Tentar obter telefone de múltiplas fontes
            let phone = '';
            for (let i = 0; i < 10; i++) {
                const tel = urlParams.get(`TELEFONES[${i}][TELEFONE]`);
                if (tel && tel.trim()) {
                    phone = tel;
                    break;
                }
            }
            
            params.phone = phone.replace(/[^\d]/g, '') || "";
            params.phoneFormatted = phone || "";
            return params;
        }

        // Simular a função obterDadosCompletos
        function obterDadosCompletos(queryString) {
            let userData = getUrlParams(queryString);
            
            // Verificar se falta telefone e tentar obter de outras posições
            if (!userData.phone || userData.phone.length < 10) {
                const urlParams = new URLSearchParams(queryString);
                for (let i = 0; i < 10; i++) {
                    const tel = urlParams.get(`TELEFONES[${i}][TELEFONE]`);
                    if (tel && tel.trim()) {
                        userData.phone = tel.replace(/[^\d]/g, "");
                        userData.phoneFormatted = tel;
                        break;
                    }
                }
            }

            // Se ainda não temos dados suficientes, usar padrão
            if (!userData.name || !userData.cpf || userData.cpf.length !== 11) {
                userData.name = userData.name || "Cliente";
                userData.cpf = userData.cpf || "12345678901";
                userData.cpfFormatted = formatCPF(userData.cpf);
            }

            // Garantir que sempre temos telefone válido
            if (!userData.phone || userData.phone.length < 10) {
                userData.phone = "11999999999";
                userData.phoneFormatted = "(11) 99999-9999";
                console.log('Usando telefone padrão:', userData.phone);
            }

            return userData;
        }

        function formatCPF(cpf) {
            return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
        }

        function testarUrlProblematica() {
            const queryString = "?DADOS_PESSOAIS%5BPRIMEIRO_NOME%5D=Carlos&DADOS_PESSOAIS%5BNOME%5D=Carlos+Eduardo+Rocha+Sousa&DADOS_PESSOAIS%5BNOME_MAE%5D=&DADOS_PESSOAIS%5BNOME_PAI%5D=&DADOS_PESSOAIS%5BCPF%5D=708.853.601-33&DADOS_PESSOAIS%5BSEXO%5D=Masculino&DADOS_PESSOAIS%5BRENDA%5D=225%2C84&DADOS_PESSOAIS%5BRG%5D=&DADOS_PESSOAIS%5BDATA_NASCIMENTO%5D=02%2F07%2F2002";
            
            const resultado = obterDadosCompletos(queryString);
            
            document.getElementById('resultado-problematica').style.display = 'block';
            document.getElementById('dados-problematica').textContent = JSON.stringify(resultado, null, 2);
        }

        function testarUrlFuncionando() {
            const queryString = "?DADOS_PESSOAIS%5BPRIMEIRO_NOME%5D=Matheus&DADOS_PESSOAIS%5BNOME%5D=Matheus+Henrique+Souza+Silva&DADOS_PESSOAIS%5BNOME_MAE%5D=Silvia+Patricia+e+Souza&DADOS_PESSOAIS%5BNOME_PAI%5D=&DADOS_PESSOAIS%5BCPF%5D=470.202.568-64&DADOS_PESSOAIS%5BSEXO%5D=Masculino&DADOS_PESSOAIS%5BRENDA%5D=457%2C59&DADOS_PESSOAIS%5BRG%5D=&DADOS_PESSOAIS%5BDATA_NASCIMENTO%5D=11%2F11%2F1996&TELEFONES%5B0%5D%5BTELEFONE%5D=%2811%29+59345269&TELEFONES%5B1%5D%5BTELEFONE%5D=%2813%29+34817114&TELEFONES%5B2%5D%5BTELEFONE%5D=%2811%29+59313461&TELEFONES%5B3%5D%5BTELEFONE%5D=%2811%29+942361137&TELEFONES%5B4%5D%5BTELEFONE%5D=%2811%29+984817728&ENDERECOS%5B0%5D%5BCIDADE%5D=Sao+paulo&ENDERECOS%5B0%5D%5BNUMERO%5D=84&ENDERECOS%5B0%5D%5BCEP%5D=04849016&ENDERECOS%5B0%5D%5BUF%5D=SP&ENDERECOS%5B0%5D%5BBAIRRO%5D=Prq+residencial+cocaia&ENDERECOS%5B0%5D%5BLOGRADOURO%5D=TvArroio+pimenta";
            
            const resultado = obterDadosCompletos(queryString);
            
            document.getElementById('resultado-funcionando').style.display = 'block';
            document.getElementById('dados-funcionando').textContent = JSON.stringify(resultado, null, 2);
        }
    </script>
</body>
</html>
