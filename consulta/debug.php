<?php
// Debug simples
echo "Content-Type: text/plain\n\n";

echo "=== DEBUG SIMPLES ===\n";

// Verificar arquivo de chave
$chave_file = '../includes/url.txt';
echo "Arquivo de chave: $chave_file\n";
echo "Existe: " . (file_exists($chave_file) ? "SIM" : "NÃO") . "\n";

if (file_exists($chave_file)) {
    $chave = file_get_contents($chave_file);
    echo "Conteúdo: '" . trim($chave) . "'\n";
    echo "É padrão: " . (trim($chave) === 'defina sua chave secreta' ? "SIM" : "NÃO") . "\n";
} else {
    echo "ERRO: Arquivo não existe!\n";
}

echo "\n";

// Verificar se consegue fazer uma requisição simples
echo "Teste de conectividade:\n";
$url = 'https://httpbin.org/get';
echo "Testando: $url\n";

$ch = curl_init($url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
$result = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $http_code\n";
echo "Error: " . ($error ?: "Nenhum") . "\n";
echo "Response length: " . strlen($result) . "\n";

echo "\n=== FIM ===\n";
?>
