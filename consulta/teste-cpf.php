<?php
// Teste específico para o CPF 70885360133

function validaCPF($cpf) {
    $cpf = preg_replace('/[^0-9]/', '', $cpf);
    if (strlen($cpf) != 11) return false;
    if (preg_match('/(\d)\1{10}/', $cpf)) return false;
    
    for ($t = 9; $t < 11; $t++) {
        for ($d = 0, $c = 0; $c < $t; $c++) {
            $d += $cpf[$c] * (($t + 1) - $c);
        }
        $d = ((10 * $d) % 11) % 10;
        if ($cpf[$c] != $d) return false;
    }
    return true;
}

$cpf = '70885360133';
echo "CPF: $cpf\n";
echo "Válido: " . (validaCPF($cpf) ? 'SIM' : 'NÃO') . "\n";
echo "Tamanho: " . strlen($cpf) . " dígitos\n";

// Teste da API
$payload = [
    'name' => 'Teste Usuario',
    'email' => '<EMAIL>',
    'cpf' => $cpf,
    'phone' => '11999999999',
    'paymentMethod' => 'PIX',
    'amount' => 7651,
    'traceable' => true,
    'items' => [
        [
            'unitPrice' => 7651,
            'title' => 'TAXA DE REGULARIZAÇÃO',
            'quantity' => 1,
            'tangible' => false
        ]
    ]
];

echo "\nTestando API...\n";

$ch = curl_init('https://pay.prosperidadepay.com.br/api/v1/transaction.purchase');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: a0c5d35b-dbd3-4f3d-9d7f-f3ec0263447a'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);

$start = microtime(true);
$result = curl_exec($ch);
$end = microtime(true);
$rcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

echo "Tempo: " . round(($end - $start) * 1000, 2) . "ms\n";
echo "HTTP Code: $rcode\n";

if ($curl_error) {
    echo "CURL Error: $curl_error\n";
} else {
    echo "Resposta (primeiros 200 chars): " . substr($result, 0, 200) . "\n";
    
    $data = json_decode($result, true);
    if ($data) {
        if (isset($data['id'])) {
            echo "✅ PIX criado com sucesso! ID: " . $data['id'] . "\n";
        } else if (isset($data['message'])) {
            echo "❌ Erro da API: " . $data['message'] . "\n";
        } else {
            echo "⚠️ Resposta inesperada da API\n";
        }
    } else {
        echo "❌ Resposta não é JSON válido\n";
    }
}
?>
