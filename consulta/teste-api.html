<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste API de Dados</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { margin-top: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9; }
        .error { border-color: #dc3545; background: #f8d7da; color: #721c24; }
        .success { border-color: #28a745; background: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Teste API de Dados</h1>
        
        <div class="form-group">
            <label for="cpf">CPF (apenas números):</label>
            <input type="text" id="cpf" placeholder="12345678901" maxlength="11">
        </div>
        
        <button onclick="testarAPI()">Consultar Dados</button>
        
        <div id="resultado" class="result" style="display: none;">
            <h3>Resultado:</h3>
            <pre id="dados"></pre>
        </div>
    </div>

    <script>
        async function testarAPI() {
            const cpf = document.getElementById('cpf').value.replace(/\D/g, '');
            const resultado = document.getElementById('resultado');
            const dados = document.getElementById('dados');
            
            if (!cpf || cpf.length !== 11) {
                resultado.className = 'result error';
                resultado.style.display = 'block';
                dados.textContent = 'Por favor, insira um CPF válido com 11 dígitos.';
                return;
            }
            
            try {
                console.log('Consultando CPF:', cpf);
                const response = await fetch(`api-dados.php?cpf=${cpf}`);
                const data = await response.json();
                
                resultado.style.display = 'block';
                
                if (response.ok && data.success) {
                    resultado.className = 'result success';
                    dados.textContent = JSON.stringify(data, null, 2);
                } else {
                    resultado.className = 'result error';
                    dados.textContent = JSON.stringify(data, null, 2);
                }
            } catch (error) {
                resultado.className = 'result error';
                resultado.style.display = 'block';
                dados.textContent = 'Erro ao consultar API: ' + error.message;
            }
        }
        
        // Permitir consulta ao pressionar Enter
        document.getElementById('cpf').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testarAPI();
            }
        });
    </script>
</body>
</html>
