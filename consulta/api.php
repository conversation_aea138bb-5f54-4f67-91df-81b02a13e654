<?php
// Verifica se o CPF foi enviado via GET
if (isset($_GET['cpf'])) {
    $cpf = $_GET['cpf'];
    // Remove caracteres não numéricos do CPF
    $cpf = preg_replace('/\D/', '', $cpf);
    
    // Verifica se o CPF tem 11 dígitos
    if (strlen($cpf) !== 11) {
        header("Location: index.php?error=cpf_invalido");
        exit;
    }
    
    // Endpoint da API MyTrust
    $api_url = "https://api.mytrust.space/v1/cpf/{$cpf}";
    
    // Faz a requisição à API usando cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'x-trust-key: sk_01jv74jy9vqw2b183cvmrmptwg01jv74jy9wmgh950m9mstkbf61',
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    // Verifica se houve erro na requisição cURL
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        header("Location: index.php?error=erro_requisicao&message=" . urlencode($error));
        exit;
    }
    
    curl_close($ch);
    
    // Verifica se a requisição foi bem-sucedida
    if ($http_code == 200) {
        // Decodifica o JSON retornado pela API
        $data = json_decode($response, true);
        
        // Verifica se a resposta contém os dados esperados
        // Nota: ajuste as verificações de acordo com a estrutura de resposta real da API MyTrust
        if (isset($data['data'])) {
            $dados = $data['data'];
            
            // Redireciona para a próxima página passando os dados como parâmetros GET
            $query_string = http_build_query($dados);
            header("Location: index.php?$query_string");
            exit;
        } else {
            // Caso a API retorne dados em formato inesperado
            header("Location: index.php?error=dados_nao_encontrados");
            exit;
        }
    } else {
        // Caso a requisição à API falhe
        header("Location: index.php?error=servico_indisponivel&http_code=" . $http_code);
        exit;
    }
} else {
    // Caso o CPF não tenha sido enviado
    header("Location: index.php?error=cpf_nao_informado");
    exit;
}
?>
