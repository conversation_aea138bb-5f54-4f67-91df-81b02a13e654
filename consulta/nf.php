<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <script src="https://kit.fontawesome.com/82ab2e9a91.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emissão de NF</title>
</head>
<body>
    <header class="p-4">
        <img class="h-15" src="../assets/imagens/logo.webp">
    </header>
    
    <main class="p-4">
        <div class="bg-gray-300 h-120 p-5 rounded-lg">
            <div class="flex justify-between p-4">
                <span>ID DO PEDIDO:</span>
                <span>#9384312B</span>
            </div>
            <div class="bg-white w-full rounded-lg">
                <h1 class="p-4 bg-black rounded-lg text-white">Status do Pedido</h1>
                <div class="p-4 flex items-center">
                    <i class="fa-solid fa-circle-check text-[30px] p-2 text-green-500"></i> <span>Pagamento Confirmado!</span>
                </div>
                <div class="p-4 flex items-center">
                    <i class="fa-solid fa-circle-exclamation text-[30px] p-2 text-yellow-500"></i> <span>Emita sua nota fiscal eletrônica (NF-e)</span>
                </div>
            </div>
            <div class="p-2 text-center text-green-700 mb-4 mt-2">
                <p class="text-xs">Parabéns! Seu pagamento foi confirmado com sucesso, agora é necessário realizar a emissão da sua Nota Fiscal Eletrônica. Clique abaixo para prosseguir!</p>
            </div>
            <button id="emitirNota" class="w-full bg-black text-white p-3 rounded-lg">Emitir Nota Fiscal</button>
        </div>
    </main>
    
    <script>
    document.getElementById('emitirNota').addEventListener('click', function () {
        // Pega os parâmetros da URL atual
        const queryString = window.location.search;

        // Redireciona para a nova página com os mesmos parâmetros
        window.location.href = 'nf2.php' + queryString;
    });
    </script>
    
</body>
</html>