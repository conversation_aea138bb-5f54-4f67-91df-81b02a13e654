<?php
// Debug direto da requisição
$data = [
    "name" => "<PERSON><PERSON>",
    "email" => "<EMAIL>", 
    "cpf" => "47020256864",
    "phone" => "1159345269"
];

// Simular a requisição POST
$_GET['action'] = 'create';
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';

// Simular o input JSON
$json_input = json_encode($data);
file_put_contents('php://temp', $json_input);

// Incluir o arquivo API
ob_start();
include 'api-pix.php';
$output = ob_get_clean();

echo "Output: " . $output . "\n";
?>
